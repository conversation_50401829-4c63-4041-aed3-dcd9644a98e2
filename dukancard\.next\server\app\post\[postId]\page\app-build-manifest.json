{"pages": {"/post/[postId]/page": ["static/chunks/node_modules_next_dist_1a6ee436._.js", "static/chunks/app_favicon_ico_mjs_659ce808._.js", "static/chunks/[root-of-the-server]__acbb9135._.css", "static/chunks/_faab6129._.js", "static/chunks/app_layout_tsx_c0237562._.js", "static/chunks/_f2cc4642._.js", "static/chunks/app_post_[postId]_error_tsx_0f887fde._.js", "static/chunks/node_modules_09cb0a30._.js", "static/chunks/_79b94857._.js", "static/chunks/app_post_[postId]_loading_tsx_0f887fde._.js", "static/chunks/node_modules_next_dist_b758c999._.js", "static/chunks/app_post_[postId]_not-found_tsx_0f887fde._.js", "static/chunks/_55fa70c1._.js", "static/chunks/_6a578661._.js", "static/chunks/node_modules_next_48665b55._.js", "static/chunks/node_modules_framer-motion_dist_es_ad8b9672._.js", "static/chunks/node_modules_motion-dom_dist_es_eae4c9f6._.js", "static/chunks/node_modules_tailwind-merge_dist_bundle-mjs_mjs_b854acb4._.js", "static/chunks/node_modules_@supabase_auth-js_dist_module_6004bf20._.js", "static/chunks/node_modules_@dnd-kit_core_dist_core_esm_deb93fb9.js", "static/chunks/node_modules_html5-qrcode_esm_62b22b9a._.js", "static/chunks/node_modules_html5-qrcode_third_party_zxing-js_umd_df022588.js", "static/chunks/node_modules_@radix-ui_7a78c17e._.js", "static/chunks/node_modules_@supabase_4230e2a7._.js", "static/chunks/node_modules_@floating-ui_9ec1fa39._.js", "static/chunks/node_modules_5c40556a._.js", "static/chunks/components_qr_qr-scanner_32eee468.css", "static/chunks/app_post_[postId]_page_tsx_0f887fde._.js", "static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js", "static/chunks/node_modules_next_dist_compiled_2ce9398a._.js", "static/chunks/node_modules_next_dist_client_8f19e6fb._.js", "static/chunks/node_modules_next_dist_2ecbf5fa._.js", "static/chunks/node_modules_@swc_helpers_cjs_00636ac3._.js", "static/chunks/_e69f0d32._.js", "static/chunks/_93808211._.js"]}}