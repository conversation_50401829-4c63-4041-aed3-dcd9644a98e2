{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/post/%5BpostId%5D/error.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { AlertCircle, RefreshCw, Home } from 'lucide-react';\r\n\r\ninterface ErrorProps {\r\n  error: Error & { digest?: string };\r\n  reset: () => void;\r\n}\r\n\r\n/**\r\n * Error boundary for single post pages\r\n */\r\nexport default function Error({ error, reset }: ErrorProps) {\r\n  useEffect(() => {\r\n    // Log the error to an error reporting service\r\n    console.error('Single post page error:', error);\r\n  }, [error]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white dark:bg-black flex items-center justify-center px-4\">\r\n      <div className=\"max-w-md w-full text-center\">\r\n        <div className=\"mb-8\">\r\n          <AlertCircle className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\r\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\r\n            Something went wrong\r\n          </h1>\r\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\r\n            We encountered an error while loading this post. This might be a temporary issue.\r\n          </p>\r\n          {process.env.NODE_ENV === 'development' && (\r\n            <details className=\"text-left bg-gray-100 dark:bg-gray-800 p-4 rounded-lg mb-4\">\r\n              <summary className=\"cursor-pointer font-medium text-gray-900 dark:text-white mb-2\">\r\n                Error Details\r\n              </summary>\r\n              <pre className=\"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap\">\r\n                {error.message}\r\n              </pre>\r\n            </details>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"space-y-3\">\r\n          <button\r\n            onClick={reset}\r\n            className=\"inline-flex items-center justify-center w-full px-4 py-2 bg-[var(--brand-gold)] text-white font-medium rounded-lg transition-colors\"\r\n          >\r\n            <RefreshCw className=\"w-4 h-4 mr-2\" />\r\n            Try Again\r\n          </button>\r\n          \r\n          <Link\r\n            href=\"/\"\r\n            className=\"inline-flex items-center justify-center w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\r\n          >\r\n            <Home className=\"w-4 h-4 mr-2\" />\r\n            Go to Homepage\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAce,SAAS,MAAM,EAAE,KAAK,EAAE,KAAK,EAAc;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,QAAQ,KAAK,CAAC,2BAA2B;IAC3C,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;wBAGpD,oDAAyB,+BACxB,8OAAC;4BAAQ,WAAU;;8CACjB,8OAAC;oCAAQ,WAAU;8CAAgE;;;;;;8CAGnF,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO;;;;;;;;;;;;;;;;;;8BAMtB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}]}