
'use server';

import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { COLUMNS, TABLES } from "@/lib/supabase/constants";
import { createClient } from "@/utils/supabase/server";
import { TablesInsert } from "@/types/supabase";

// getAuthenticatedUser
async function getAuthenticatedUser() {
  const supabase = await createClient();
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) {
      console.error("Error fetching authenticated user:", error.message);
      return { user: null, error: "User not found or authentication error." };
    }
    return { user, error: null };
  } catch (err) {
    console.error("Unexpected error fetching authenticated user:", err);
    return { user: null, error: "An unexpected error occurred." };
  }
}

// checkIfCustomerProfileExists
async function checkIfCustomerProfileExists(userId: string) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, userId)
      .maybeSingle();

    if (error) {
      console.error("Error checking existing profile:", error.message);
      return { exists: false, error: "Database error checking profile." };
    }

    return { exists: !!data, error: null };
  } catch (err) {
    console.error("Unexpected error checking profile:", err);
    return { exists: false, error: "An unexpected error occurred." };
  }
}

// createUserProfile
async function createUserProfile(profile: TablesInsert<'customer_profiles'>) {
  const supabase = await createClient();
  try {
    const { data, error } = await supabase
      .from(TABLES.CUSTOMER_PROFILES)
      .insert([profile])
      .select()
      .single();

    if (error) {
      console.error("Error creating user profile:", error.message);
      return { data: null, error: error.message };
    }
    return { data: Array.isArray(data) ? data[0] || null : data || null, error: null };
  } catch (err) {
    console.error("Unexpected error creating user profile:", err);
    return { data: null, error: "An unexpected error occurred." };
  }
}

export async function createCustomerProfileAction(
  userId: string,
  redirectSlug: string | null = null,
  _message: string | null = null
) {
  if (!userId) {
    return { error: "User ID is required." };
  }

  // Fetch user details first
  const { user, error: userError } = await getAuthenticatedUser();

  if (userError || !user) {
    // Error fetching user in action
    return { error: "User not found or authentication error." };
  }

  // Check if customer profile already exists
  const { exists, error: checkError } = await checkIfCustomerProfileExists(userId);

  if (checkError) {
    return { error: checkError };
  }

  if (exists) {
    // Profile already exists for user
    if (redirectSlug) {
      redirect(`/${redirectSlug}`);
    } else {
      redirect("/dashboard/customer");
    }
  }

  // Create the customer profile
  const { error: insertError } = await createUserProfile({
    [COLUMNS.ID]: userId,
    [COLUMNS.NAME]: user.user_metadata?.full_name ?? user.user_metadata?.name ?? null,
    [COLUMNS.EMAIL]: user.email ?? null,
  });

  if (insertError) {
    console.error("Error creating customer profile:", insertError);
    return { error: "Failed to create profile." };
  }

  // Revalidate relevant paths
  revalidatePath("/choose-role");
  revalidatePath("/dashboard/customer");

  // Redirect to the appropriate page
  if (redirectSlug) {
    redirect(`/${redirectSlug}`);
  } else {
    redirect("/dashboard/customer");
  }
}
