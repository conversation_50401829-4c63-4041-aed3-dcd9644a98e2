// TypeScript types for products with variant support

import { VariantStats } from "./variants";
import { Tables, TablesInsert, TablesUpdate } from "@/types/supabase";

export type ProductsServices = Tables<'products_services'>;
export type ProductsServicesInsert = TablesInsert<'products_services'>;
export type ProductsServicesUpdate = TablesUpdate<'products_services'>;

import { ProductVariant } from "./variants";

// Re-export the base ProductServiceData type from schemas
export type { ProductServiceData } from "@/app/(dashboard)/dashboard/business/products/actions/schemas";

// Extended product type with variant information
export interface ProductWithVariantInfo extends ProductsServices {
  // Variant-specific fields
  variant_count: number;
  has_variants: boolean;
  available_variant_count: number;
  variants?: ProductVariant[];
}

// Product with full variant details (for detailed views)
export interface ProductWithVariants extends ProductWithVariantInfo {
  variants: ProductVariant[];
}

// Product list item with variant summary (for list/grid views)
export interface ProductListItem extends ProductWithVariantInfo {
  variants?: never; // Explicitly exclude full variant data for list views
}

// Product creation/update types
export type CreateProductData = ProductsServicesInsert;
export type UpdateProductData = ProductsServicesUpdate;

// Product with variants creation (for products that will have variants)
export interface CreateProductWithVariantsData extends CreateProductData {
  create_variants?: boolean;
  initial_variants?: Array<{
    variant_name: string;
    variant_values: Record<string, string>;
    base_price?: number;
    discounted_price?: number;
    is_available?: boolean;
    images?: string[];
  }>;
}

// Product filters and sorting
export interface ProductFilters {
  searchTerm?: string;
  filterAvailable?: boolean;
  hasVariants?: boolean;
  productType?: "physical" | "service";
  priceRange?: {
    min?: number;
    max?: number;
  };
}

export type ProductSortBy =
  | "created_asc"
  | "created_desc"
  | "updated_asc"
  | "updated_desc"
  | "price_asc"
  | "price_desc"
  | "name_asc"
  | "name_desc"
  | "variant_count_asc"
  | "variant_count_desc";

// API response types
export interface ProductApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface GetProductsResponse {
  data: ProductListItem[];
  count: number;
  page: number;
  totalPages: number;
  hasMore: boolean;
}

export interface GetProductWithVariantsResponse {
  data: ProductWithVariants;
}

// Business product statistics
export interface ProductStats extends VariantStats {
  total_revenue?: number;
  average_price?: number;
  most_popular_product?: {
    id: string;
    name: string;
    view_count?: number;
  };
}

// Product operation types
export type ProductOperation =
  | "create"
  | "update"
  | "delete"
  | "toggle_availability";

export interface ProductOperationResult {
  success: boolean;
  product?: ProductWithVariantInfo;
  error?: string;
}

// Bulk product operations
export interface BulkProductOperation {
  product_ids: string[];
  operation: "enable" | "disable" | "delete" | "update_price";
  data?: {
    is_available?: boolean;
    base_price?: number;
    discounted_price?: number;
  };
}

export interface BulkProductResult {
  success: boolean;
  updated_count: number;
  failed_count: number;
  errors?: string[];
}

// Product validation constraints
export const PRODUCT_CONSTRAINTS = {
  MAX_NAME_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_IMAGES_PER_PRODUCT: 5,
  MIN_PRICE: 0.01,
  MAX_PRICE: 999999.99,
} as const;

// Product type utilities
export const PRODUCT_TYPES = ["physical", "service"] as const;
export type ProductType = (typeof PRODUCT_TYPES)[number];

// Product status types
export interface ProductStatus {
  is_available: boolean;
  has_variants: boolean;
  variant_availability: {
    total: number;
    available: number;
    unavailable: number;
  };
}

// Product image types
export interface ProductImage {
  url: string;
  index: number;
  is_featured: boolean;
  alt_text?: string;
}

export interface ProductImageUpload {
  file: File;
  preview_url: string;
  is_featured: boolean;
}

// Product search and discovery types
export interface ProductSearchResult extends ProductListItem {
  business_name: string;
  business_slug: string;
  business_logo_url?: string;
  distance?: number; // For location-based search
  relevance_score?: number; // For search ranking
}

export interface ProductSearchFilters extends ProductFilters {
  location?: {
    latitude: number;
    longitude: number;
    radius?: number; // in kilometers
  };
  business_category?: string;
  city?: string;
  state?: string;
}

// Product analytics types
export interface ProductAnalytics {
  product_id: string;
  views: number;
  inquiries: number;
  shares: number;
  conversion_rate?: number;
  popular_variants?: Array<{
    variant_id: string;
    variant_name: string;
    selection_count: number;
  }>;
}

// Export utility functions type
export interface ProductUtils {
  formatPrice: (price: number | null | undefined) => string;
  getProductStatus: (product: ProductWithVariantInfo) => ProductStatus;
  getDisplayPrice: (product: ProductWithVariantInfo) => number | null;
  hasDiscount: (product: ProductWithVariantInfo) => boolean;
  getDiscountPercentage: (product: ProductWithVariantInfo) => number | null;
  getVariantSummary: (product: ProductWithVariantInfo) => string;
}
