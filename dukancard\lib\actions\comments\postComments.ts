import { createClient } from '@/utils/supabase/client';
import { TABLES, COLUMNS, RPC_FUNCTIONS } from '@/lib/supabase/constants';
import type {
  CreateCommentRequest,
  EditCommentRequest,
  DeleteCommentRequest,
  PinCommentRequest,
  UnpinCommentRequest,
  CreateCommentResponse,
  EditCommentResponse,
  DeleteCommentResponse,
  PinCommentResponse,
  UnpinCommentResponse,
  PostCommentWithUser,
  CommentFilters,
} from '@/types/like-comment';

/**
 * Create a new comment on a post
 */
export async function createComment(
  postId: string,
  postSource: 'business' | 'customer',
  content: string,
  parentCommentId?: string
): Promise<CreateCommentResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.CREATE_COMMENT as any, {
      p_post_id: postId,
      p_post_source: postSource,
      p_content: content,
      p_parent_comment_id: parentCommentId || null,
    } as CreateCommentRequest);

    if (error) {
      console.error('Error creating comment:', error);
      return {
        success: false,
        message: 'Failed to create comment',
        error: error.message,
      };
    }

    return data as unknown as CreateCommentResponse;
  } catch (error) {
    console.error('Error in createComment:', error);
    return {
      success: false,
      message: 'Failed to create comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Edit an existing comment
 */
export async function editComment(
  commentId: string,
  content: string
): Promise<EditCommentResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.EDIT_COMMENT, {
      p_comment_id: commentId,
      p_content: content,
    } as EditCommentRequest);

    if (error) {
      console.error('Error editing comment:', error);
      return {
        success: false,
        message: 'Failed to edit comment',
        error: error.message,
      };
    }

    return data as unknown as EditCommentResponse;
  } catch (error) {
    console.error('Error in editComment:', error);
    return {
      success: false,
      message: 'Failed to edit comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Delete a comment
 */
export async function deleteComment(commentId: string): Promise<DeleteCommentResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.DELETE_COMMENT, {
      p_comment_id: commentId,
    } as DeleteCommentRequest);

    if (error) {
      console.error('Error deleting comment:', error);
      return {
        success: false,
        message: 'Failed to delete comment',
        error: error.message,
      };
    }

    return data as unknown as DeleteCommentResponse;
  } catch (error) {
    console.error('Error in deleteComment:', error);
    return {
      success: false,
      message: 'Failed to delete comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Pin a comment (post owner only)
 */
export async function pinComment(commentId: string): Promise<PinCommentResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.PIN_COMMENT, {
      p_comment_id: commentId,
    } as PinCommentRequest);

    if (error) {
      console.error('Error pinning comment:', error);
      return {
        success: false,
        message: 'Failed to pin comment',
        error: error.message,
      };
    }

    return data as unknown as PinCommentResponse;
  } catch (error) {
    console.error('Error in pinComment:', error);
    return {
      success: false,
      message: 'Failed to pin comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Unpin a comment (post owner only)
 */
export async function unpinComment(commentId: string): Promise<UnpinCommentResponse> {
  try {
    const supabase = createClient();

    const { data, error } = await supabase.rpc(RPC_FUNCTIONS.UNPIN_COMMENT, {
      p_comment_id: commentId,
    } as UnpinCommentRequest);

    if (error) {
      console.error('Error unpinning comment:', error);
      return {
        success: false,
        message: 'Failed to unpin comment',
        error: error.message,
      };
    }

    return data as unknown as UnpinCommentResponse;
  } catch (error) {
    console.error('Error in unpinComment:', error);
    return {
      success: false,
      message: 'Failed to unpin comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get comments for a post with user information
 */
export async function getPostComments(
  filters: CommentFilters
): Promise<{ success: boolean; data: PostCommentWithUser[]; error?: string }> {
  try {
    const supabase = createClient();
    const { post_id, post_source, sort_order = 'pinned_first', limit = 20, offset = 0 } = filters;

    // Build the query
    let query = supabase
      .from(TABLES.POST_COMMENTS)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.USER_ID},
        ${COLUMNS.POST_ID},
        ${COLUMNS.POST_SOURCE},
        ${COLUMNS.PARENT_COMMENT_ID},
        ${COLUMNS.CONTENT},
        ${COLUMNS.IS_PINNED},
        ${COLUMNS.IS_EDITED},
        ${COLUMNS.EDITED_AT},
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.UPDATED_AT}
      `)
      .eq(COLUMNS.POST_ID, post_id)
      .eq(COLUMNS.POST_SOURCE, post_source)
      .is(COLUMNS.PARENT_COMMENT_ID, null); // Only top-level comments

    // Apply sorting
    if (sort_order === 'pinned_first') {
      query = query.order(COLUMNS.IS_PINNED, { ascending: false })
                   .order(COLUMNS.CREATED_AT, { ascending: true });
    } else if (sort_order === 'newest') {
      query = query.order(COLUMNS.CREATED_AT, { ascending: false });
    } else {
      query = query.order(COLUMNS.CREATED_AT, { ascending: true });
    }

    const { data: comments, error } = await query.range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching comments:', error);
      return {
        success: false,
        data: [],
        error: error.message,
      };
    }

    // TODO: Enhance with user information and reply counts
    // For now, return basic comment data
    const commentsWithUser: PostCommentWithUser[] = (comments || []).map(comment => ({
      ...comment,
      user_name: 'User', // TODO: Fetch from user profiles
      user_type: 'customer' as const, // TODO: Determine from user data
      like_count: 0, // TODO: Fetch comment like count
      is_liked_by_current_user: false, // TODO: Check if current user liked
      replies: [], // TODO: Fetch replies
    }));

    return {
      success: true,
      data: commentsWithUser,
    };
  } catch (error) {
    console.error('Error in getPostComments:', error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
