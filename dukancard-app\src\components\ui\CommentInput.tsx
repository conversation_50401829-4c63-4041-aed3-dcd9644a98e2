import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  StyleSheet,
  Keyboard,
  Animated,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Send, X } from 'lucide-react-native';
import { useLikeCommentStore } from '@/src/stores/simpleLikeCommentStore';

interface CommentInputProps {
  postId: string;
  postSource: 'business' | 'customer';
  parentCommentId?: string;
  placeholder?: string;
  maxLength?: number;
  autoFocus?: boolean;
  onSubmit?: (content: string) => void;
  onCancel?: () => void;
  showCancel?: boolean;
  disabled?: boolean;
  replyToUser?: string;
}

export function CommentInput({
  postId,
  postSource,
  parentCommentId,
  placeholder = 'Write a comment...',
  maxLength = 500,
  autoFocus = false,
  onSubmit,
  onCancel,
  showCancel = false,
  disabled = false,
  replyToUser,
}: CommentInputProps) {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const textInputRef = useRef<TextInput>(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  const { createComment } = useLikeCommentStore();

  // Auto-focus if requested
  useEffect(() => {
    if (autoFocus && textInputRef.current) {
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 100);
    }
  }, [autoFocus]);

  // Animate character counter when focused
  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: isFocused ? 1 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [isFocused, fadeAnim]);

  const handleSubmit = async () => {
    const trimmedContent = content.trim();
    if (!trimmedContent || isSubmitting || disabled) return;

    // Dismiss keyboard
    Keyboard.dismiss();
    setIsSubmitting(true);
    
    try {
      const success = await createComment(postId, postSource, trimmedContent, parentCommentId);
      
      if (success) {
        setContent('');
        onSubmit?.(trimmedContent);
        Alert.alert('Success', 'Comment posted successfully!');
      } else {
        Alert.alert('Error', 'Failed to post comment. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
      Alert.alert('Error', 'Failed to post comment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    setContent('');
    Keyboard.dismiss();
    onCancel?.();
  };

  const remainingChars = maxLength - content.length;
  const isOverLimit = remainingChars < 0;
  const isNearLimit = remainingChars <= 50;
  const canSubmit = content.trim().length > 0 && !isOverLimit && !isSubmitting && !disabled;

  const getCharCountColor = () => {
    if (isOverLimit) return '#ef4444';
    if (isNearLimit) return '#f59e0b';
    return '#6b7280';
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}
    >
      <View style={styles.inputContainer}>
        {/* Reply indicator */}
        {replyToUser && (
          <View style={styles.replyIndicator}>
            <Text style={styles.replyText}>Replying to {replyToUser}</Text>
            {showCancel && (
              <TouchableOpacity onPress={handleCancel} style={styles.replyCancel}>
                <X size={16} color="#6b7280" />
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Input field */}
        <View style={[styles.inputWrapper, isFocused && styles.inputWrapperFocused]}>
          <TextInput
            ref={textInputRef}
            style={[
              styles.textInput,
              isOverLimit && styles.textInputError,
            ]}
            value={content}
            onChangeText={setContent}
            placeholder={placeholder}
            placeholderTextColor="#9ca3af"
            multiline
            maxLength={maxLength + 50} // Allow slight overflow for better UX
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            editable={!disabled && !isSubmitting}
            textAlignVertical="top"
            returnKeyType="default"
            blurOnSubmit={false}
          />

          {/* Submit button */}
          <TouchableOpacity
            style={[
              styles.submitButton,
              canSubmit ? styles.submitButtonActive : styles.submitButtonInactive,
            ]}
            onPress={handleSubmit}
            disabled={!canSubmit}
          >
            <Send
              size={20}
              color={canSubmit ? '#ffffff' : '#9ca3af'}
            />
          </TouchableOpacity>
        </View>

        {/* Footer */}
        <Animated.View style={[styles.footer, { opacity: fadeAnim }]}>
          <View style={styles.footerLeft}>
            {showCancel && !replyToUser && (
              <TouchableOpacity
                onPress={handleCancel}
                disabled={isSubmitting}
                style={styles.cancelButton}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.footerRight}>
            {/* Character count */}
            <Text style={[styles.charCount, { color: getCharCountColor() }]}>
              {isNearLimit || isOverLimit ? (
                `${remainingChars} ${remainingChars === 1 ? 'char' : 'chars'} ${remainingChars < 0 ? 'over' : 'left'}`
              ) : (
                `${content.length}/${maxLength}`
              )}
            </Text>
          </View>
        </Animated.View>

        {/* Error message */}
        {isOverLimit && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>
              Comment is too long. Please shorten it by {Math.abs(remainingChars)} characters.
            </Text>
          </View>
        )}

        {/* Loading indicator */}
        {isSubmitting && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Posting comment...</Text>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
  },
  inputContainer: {
    padding: 16,
  },
  replyIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 12,
  },
  replyText: {
    fontSize: 14,
    color: '#6b7280',
    fontStyle: 'italic',
  },
  replyCancel: {
    padding: 4,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
  },
  inputWrapperFocused: {
    borderColor: '#3b82f6',
    shadowColor: '#3b82f6',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
    maxHeight: 120,
    minHeight: 20,
    paddingVertical: 0,
    marginRight: 12,
  },
  textInputError: {
    color: '#ef4444',
  },
  submitButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonActive: {
    backgroundColor: '#3b82f6',
  },
  submitButtonInactive: {
    backgroundColor: '#e5e7eb',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingHorizontal: 4,
  },
  footerLeft: {
    flex: 1,
  },
  footerRight: {
    alignItems: 'flex-end',
  },
  cancelButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  cancelButtonText: {
    fontSize: 14,
    color: '#6b7280',
  },
  charCount: {
    fontSize: 12,
    fontWeight: '500',
  },
  errorContainer: {
    marginTop: 8,
    paddingHorizontal: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#ef4444',
    fontWeight: '500',
  },
  loadingContainer: {
    marginTop: 8,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 12,
    color: '#6b7280',
    fontStyle: 'italic',
  },
});

export default CommentInput;
