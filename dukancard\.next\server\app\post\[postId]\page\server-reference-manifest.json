{"node": {"00a00d491267a07d935fb19df8455dd8bd171b1c9c": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "407121b23009beabae53a7f4a46f8b93b8efac1e33": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "60682db4e70343f91b301dd30901dcafa740c88d59": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "604b6910770a0ba1046cd38659b3224574b0408040": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "4015c29807b51e86d56fec07c4557ae3c5e5720749": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "401454534d99894ef6a1d7c2a079e1017a43afe6f2": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}, "40c2f2431a4f79b36b5cb2951c7db4ff47953d5aeb": {"workers": {"app/post/[postId]/page": {"moduleId": "[project]/.next-internal/server/app/post/[postId]/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/products/fetchProductsByIds.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/posts/crud.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/shared/productActions.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/post/[postId]/page": "action-browser"}}}, "edge": {}}