'use client';

import React, { useEffect, useState } from 'react';
import { MessageCircle, Loader2, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLikeCommentStore } from '@/lib/stores/simpleLikeCommentStore';
import CommentDisplay from './CommentDisplay';
import CommentInput from './CommentInput';

interface CommentSectionProps {
  postId: string;
  postSource: 'business' | 'customer';
  currentUserId?: string;
  isPostOwner?: boolean;
  showInput?: boolean;
  maxHeight?: string;
  className?: string;
  onCommentCountChange?: (count: number) => void;
}

export function CommentSection({
  postId,
  postSource,
  currentUserId,
  isPostOwner = false,
  showInput = true,
  maxHeight = '600px',
  className,
  onCommentCountChange,
}: CommentSectionProps) {
  const [sortOrder, setSortOrder] = useState<'pinned_first' | 'newest' | 'oldest'>('pinned_first');
  
  const {
    postComments,
    loadingComments,
    loadComments,
  } = useLikeCommentStore();

  const postKey = `${postId}:${postSource}`;
  const commentsData = postComments[postKey];
  const isLoading = loadingComments.has(postKey);
  
  const comments = commentsData?.comments || [];
  const hasComments = comments.length > 0;

  // Load comments on mount
  useEffect(() => {
    loadComments(postId, postSource);
  }, [postId, postSource, loadComments]);

  // Notify parent of comment count changes
  useEffect(() => {
    onCommentCountChange?.(comments.length);
  }, [comments.length, onCommentCountChange]);

  const handleRefresh = () => {
    loadComments(postId, postSource, true);
  };

  const handleSortChange = (newSortOrder: typeof sortOrder) => {
    setSortOrder(newSortOrder);
    // TODO: Implement sorting logic in the store
  };

  // Sort comments based on selected order
  const sortedComments = React.useMemo(() => {
    const sorted = [...comments];
    
    switch (sortOrder) {
      case 'pinned_first':
        return sorted.sort((a, b) => {
          if (a.is_pinned && !b.is_pinned) return -1;
          if (!a.is_pinned && b.is_pinned) return 1;
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        });
      case 'newest':
        return sorted.sort((a, b) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      case 'oldest':
        return sorted.sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      default:
        return sorted;
    }
  }, [comments, sortOrder]);

  return (
    <div className={cn('flex flex-col', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-gray-600" />
          <h3 className="font-semibold text-gray-900">
            Comments {hasComments && `(${comments.length})`}
          </h3>
        </div>

        <div className="flex items-center gap-2">
          {/* Sort dropdown */}
          {hasComments && (
            <select
              value={sortOrder}
              onChange={(e) => handleSortChange(e.target.value as typeof sortOrder)}
              className="text-sm border border-gray-200 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="pinned_first">Pinned First</option>
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
            </select>
          )}

          {/* Refresh button */}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className={cn(
              'p-2 rounded-md border border-gray-200 hover:bg-gray-50',
              'focus:outline-none focus:ring-2 focus:ring-blue-500',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              'transition-colors duration-200'
            )}
            aria-label="Refresh comments"
          >
            <RefreshCw className={cn(
              'h-4 w-4 text-gray-600',
              isLoading && 'animate-spin'
            )} />
          </button>
        </div>
      </div>

      {/* Comment input */}
      {showInput && (
        <div className="mb-6">
          <CommentInput
            postId={postId}
            postSource={postSource}
            placeholder="Write a comment..."
          />
        </div>
      )}

      {/* Comments list */}
      <div 
        className={cn(
          'flex-1 overflow-y-auto space-y-4',
          'scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100'
        )}
        style={{ maxHeight }}
      >
        {/* Loading state */}
        {isLoading && !hasComments && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-gray-500">
              <Loader2 className="h-5 w-5 animate-spin" />
              <span>Loading comments...</span>
            </div>
          </div>
        )}

        {/* Empty state */}
        {!isLoading && !hasComments && (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <MessageCircle className="h-12 w-12 text-gray-300 mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              No comments yet
            </h4>
            <p className="text-gray-500 mb-4">
              Be the first to share your thoughts!
            </p>
            {!showInput && (
              <button
                onClick={() => {/* TODO: Focus comment input */}}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200"
              >
                Add Comment
              </button>
            )}
          </div>
        )}

        {/* Comments */}
        {hasComments && (
          <div className="space-y-6">
            {sortedComments.map((comment) => (
              <CommentDisplay
                key={comment.id}
                comment={comment}
                postId={postId}
                postSource={postSource}
                currentUserId={currentUserId}
                isPostOwner={isPostOwner}
              />
            ))}
          </div>
        )}

        {/* Loading more indicator */}
        {isLoading && hasComments && (
          <div className="flex items-center justify-center py-4">
            <div className="flex items-center gap-2 text-gray-500">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Refreshing comments...</span>
            </div>
          </div>
        )}
      </div>

      {/* Footer with stats */}
      {hasComments && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-500">
            <span>
              {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
            </span>
            
            {commentsData?.lastFetched && (
              <span>
                Last updated: {new Date(commentsData.lastFetched).toLocaleTimeString()}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Hook for comment section functionality
export function useCommentSection(postId: string, postSource: 'business' | 'customer') {
  const {
    postComments,
    loadingComments,
    loadComments,
  } = useLikeCommentStore();

  const postKey = `${postId}:${postSource}`;
  const commentsData = postComments[postKey];
  const isLoading = loadingComments.has(postKey);
  
  const comments = commentsData?.comments || [];
  const commentCount = comments.length;

  const refreshComments = () => {
    loadComments(postId, postSource, true);
  };

  // Load comments on mount
  useEffect(() => {
    loadComments(postId, postSource);
  }, [postId, postSource, loadComments]);

  return {
    comments,
    commentCount,
    isLoading,
    refreshComments,
    lastFetched: commentsData?.lastFetched,
  };
}

export default CommentSection;
