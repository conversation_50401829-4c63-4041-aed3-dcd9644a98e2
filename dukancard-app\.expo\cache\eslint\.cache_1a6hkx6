[{"C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx": "1", "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx": "2", "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx": "3", "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx": "4", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx": "5", "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx": "6", "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx": "7", "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx": "8", "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts": "9", "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx": "10", "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx": "11", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx": "12", "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx": "13", "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx": "14", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx": "15", "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx": "16", "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx": "17", "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx": "18", "C:\\web-app\\dukancard-app\\src\\components\\common\\LoadingOverlay.tsx": "19", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\BusinessCard.tsx": "20", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CategorySelector.tsx": "21", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CompactLocationPicker.tsx": "22", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\DiscoverySkeletons.tsx": "23", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ErrorComponents.tsx": "24", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\FullScreenLocationSelector.tsx": "25", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\NavigationHandlers.tsx": "26", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ResultsList.tsx": "27", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SearchSection.tsx": "28", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SortBottomSheet.tsx": "29", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\CompactLocationPickerStyles.ts": "30", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\DiscoverScreenStyles.ts": "31", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\LocationSelectorScreenStyles.ts": "32", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\ResultsListStyles.ts": "33", "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ViewToggle.tsx": "34", "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx": "35", "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx": "36", "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts": "37", "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts": "38", "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts": "39", "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts": "40", "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts": "41", "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts": "42", "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts": "43", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx": "44", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx": "45", "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx": "46", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx": "47", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx": "48", "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx": "49", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx": "50", "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx": "51", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx": "52", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx": "53", "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx": "54", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx": "55", "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx": "56", "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx": "57", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx": "58", "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx": "59", "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx": "60", "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx": "61", "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx": "62", "C:\\web-app\\dukancard-app\\src\\components\\index.ts": "63", "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx": "64", "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx": "65", "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts": "66", "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx": "67", "C:\\web-app\\dukancard-app\\src\\components\\layout\\SafeAreaWrapper.tsx": "68", "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx": "69", "C:\\web-app\\dukancard-app\\src\\components\\layout\\StatusBarManager.tsx": "70", "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx": "71", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessFollowersModal.tsx": "72", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessLikesModal.tsx": "73", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowersList.tsx": "74", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesList.tsx": "75", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductForm.tsx": "76", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsList.tsx": "77", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsSortBottomSheet.tsx": "78", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantForm.tsx": "79", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantList.tsx": "80", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageCardModal.tsx": "81", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageProductsModal.tsx": "82", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AdvancedFeaturesSection.tsx": "83", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AppearanceSection.tsx": "84", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BasicInfoSection.tsx": "85", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BusinessDetailsSection.tsx": "86", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\ContactLocationSection.tsx": "87", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\SocialLinksSection.tsx": "88", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\StatusSettingsSection.tsx": "89", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\VariantModal.tsx": "90", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\FollowingList.tsx": "91", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\LikesList.tsx": "92", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsList.tsx": "93", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsSortBottomSheet.tsx": "94", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\EditProfileModal.tsx": "95", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\FollowingModal.tsx": "96", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\LikesModal.tsx": "97", "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\ReviewsModal.tsx": "98", "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx": "99", "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx": "100", "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx": "101", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx": "102", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ColorPickerBottomSheet.tsx": "103", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx": "104", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx": "105", "C:\\web-app\\dukancard-app\\src\\components\\pickers\\VariantTypeBottomSheet.tsx": "106", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx": "107", "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx": "108", "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx": "109", "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx": "110", "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx": "111", "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx": "112", "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx": "113", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx": "114", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx": "115", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressInformationSection.tsx": "116", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx": "117", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadSection.tsx": "118", "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx": "119", "C:\\web-app\\dukancard-app\\src\\components\\profile\\PersonalInformationSection.tsx": "120", "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx": "121", "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx": "122", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx": "123", "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx": "124", "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx": "125", "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx": "126", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx": "127", "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx": "128", "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx": "129", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx": "130", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx": "131", "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx": "132", "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx": "133", "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx": "134", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx": "135", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx": "136", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts": "137", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx": "138", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx": "139", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx": "140", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\FollowingModalSkeleton.tsx": "141", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\LikesModalSkeleton.tsx": "142", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ProductsModalSkeleton.tsx": "143", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ReviewsModalSkeleton.tsx": "144", "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx": "145", "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx": "146", "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx": "147", "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx": "148", "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx": "149", "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx": "150", "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx": "151", "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx": "152", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx": "153", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx": "154", "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx": "155", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx": "156", "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts": "157", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx": "158", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx": "159", "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx": "160", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx": "161", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx": "162", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx": "163", "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts": "164", "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx": "165", "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts": "166", "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx": "167", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx": "168", "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx": "169", "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts": "170", "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx": "171", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx": "172", "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts": "173", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx": "174", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx": "175", "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts": "176", "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts": "177", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx": "178", "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx": "179", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx": "180", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx": "181", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx": "182", "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx": "183", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx": "184", "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx": "185", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx": "186", "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx": "187", "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx": "188", "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx": "189", "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts": "190", "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts": "191", "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts": "192", "C:\\web-app\\dukancard-app\\src\\constants\\predefinedVariants.ts": "193", "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx": "194", "C:\\web-app\\dukancard-app\\src\\contexts\\DiscoveryContext.tsx": "195", "C:\\web-app\\dukancard-app\\src\\contexts\\LocationContext.tsx": "196", "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx": "197", "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx": "198", "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx": "199", "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts": "200", "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts": "201", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts": "202", "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts": "203", "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts": "204", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts": "205", "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts": "206", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts": "207", "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts": "208", "C:\\web-app\\dukancard-app\\src\\hooks\\useDebounce.ts": "209", "C:\\web-app\\dukancard-app\\src\\hooks\\useDynamicSafeArea.ts": "210", "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts": "211", "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts": "212", "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts": "213", "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts": "214", "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts": "215", "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts": "216", "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts": "217", "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts": "218", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\businessActions.ts": "219", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\DiscoveryService.ts": "220", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\index.ts": "221", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\locationActions.ts": "222", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\productActions.ts": "223", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\types.ts": "224", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\locationUtils.ts": "225", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\secureBusinessProfiles.ts": "226", "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\sortMappings.ts": "227", "C:\\web-app\\dukancard-app\\src\\services\\locationStorageService.ts": "228", "C:\\web-app\\dukancard-app\\src\\types\\ad.ts": "229", "C:\\web-app\\dukancard-app\\src\\types\\auth.ts": "230", "C:\\web-app\\dukancard-app\\src\\types\\business\\analytics.ts": "231", "C:\\web-app\\dukancard-app\\src\\types\\components.ts": "232", "C:\\web-app\\dukancard-app\\src\\types\\discovery.ts": "233", "C:\\web-app\\dukancard-app\\src\\types\\index.ts": "234", "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts": "235", "C:\\web-app\\dukancard-app\\src\\types\\profile.ts": "236", "C:\\web-app\\dukancard-app\\src\\types\\screens.ts": "237", "C:\\web-app\\dukancard-app\\src\\types\\ui.ts": "238", "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts": "239", "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts": "240", "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts": "241", "C:\\web-app\\dukancard-app\\src\\utils\\distanceCalculation.ts": "242", "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts": "243", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts": "244", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts": "245", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "246", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts": "247", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts": "248", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts": "249", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts": "250", "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts": "251", "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts": "252", "C:\\web-app\\dukancard-app\\src\\utils\\imageCompression.ts": "253", "C:\\web-app\\dukancard-app\\src\\utils\\index.ts": "254", "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts": "255", "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts": "256", "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts": "257", "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts": "258", "C:\\web-app\\dukancard-app\\src\\utils\\sortMappings.ts": "259", "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts": "260", "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts": "261", "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts": "262", "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx": "263", "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx": "264", "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx": "265", "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx": "266", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx": "267", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx": "268", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx": "269", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx": "270", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx": "271", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx": "272", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx": "273", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx": "274", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx": "275", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx": "276", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx": "277", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx": "278", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx": "279", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "280", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx": "281", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx": "282", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx": "283", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx": "284", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx": "285", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx": "286", "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx": "287", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx": "288", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx": "289", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx": "290", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx": "291", "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx": "292", "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx": "293", "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx": "294", "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx": "295", "C:\\web-app\\dukancard-app\\app\\+not-found.tsx": "296", "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx": "297", "C:\\web-app\\dukancard-app\\app\\index.tsx": "298", "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx": "299", "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx": "300", "C:\\web-app\\dukancard-app\\app\\_layout.tsx": "301", "C:\\web-app\\dukancard-app\\src\\utils\\storage-paths.ts": "302", "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step1AvatarName.tsx": "303", "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step2AddressLocation.tsx": "304", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessDeleteAccountModal.tsx": "305", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessStatusSettingsModal.tsx": "306", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\GalleryModal.tsx": "307", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ShareBusinessCardModal.tsx": "308", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.styles.ts": "309", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.tsx": "310", "C:\\web-app\\dukancard-app\\src\\types\\gallery.ts": "311", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessReviewsModal.tsx": "312", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowingList.tsx": "313", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesGivenList.tsx": "314", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsGivenList.tsx": "315", "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsList.tsx": "316", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessFollowersModalSkeleton.tsx": "317", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessLikesModalSkeleton.tsx": "318", "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessReviewsModalSkeleton.tsx": "319", "C:\\web-app\\dukancard-app\\src\\config\\supabase\\constants.ts": "320", "C:\\web-app\\dukancard-app\\src\\types\\business.ts": "321", "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts": "322", "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\SocialEmptyState.tsx": "323", "C:\\web-app\\dukancard-app\\src\\components\\ui\\PostDeleteDialog.tsx": "324", "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentBottomSheet.tsx": "325", "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentDisplay.tsx": "326", "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentInput.tsx": "327", "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentSection.tsx": "328", "C:\\web-app\\dukancard-app\\src\\components\\ui\\LikeButton.tsx": "329", "C:\\web-app\\dukancard-app\\src\\stores\\likeCommentStore.ts": "330", "C:\\web-app\\dukancard-app\\src\\stores\\simpleLikeCommentStore.ts": "331", "C:\\web-app\\dukancard-app\\src\\utils\\formatNumber.ts": "332"}, {"size": 6700, "mtime": 1752078878041, "results": "333", "hashOfConfig": "334"}, {"size": 2348, "mtime": 1752078878041, "results": "335", "hashOfConfig": "334"}, {"size": 11448, "mtime": 1753463799164, "results": "336", "hashOfConfig": "334"}, {"size": 7406, "mtime": 1752078878041, "results": "337", "hashOfConfig": "334"}, {"size": 3642, "mtime": 1752843691709, "results": "338", "hashOfConfig": "334"}, {"size": 9032, "mtime": 1753101911257, "results": "339", "hashOfConfig": "334"}, {"size": 9530, "mtime": 1753452361866, "results": "340", "hashOfConfig": "334"}, {"size": 1271, "mtime": 1753462862423, "results": "341", "hashOfConfig": "334"}, {"size": 966, "mtime": 1752078878057, "results": "342", "hashOfConfig": "334"}, {"size": 11914, "mtime": 1752078878057, "results": "343", "hashOfConfig": "334"}, {"size": 9940, "mtime": 1753101911257, "results": "344", "hashOfConfig": "334"}, {"size": 6948, "mtime": 1753101911257, "results": "345", "hashOfConfig": "334"}, {"size": 10337, "mtime": 1753480242527, "results": "346", "hashOfConfig": "334"}, {"size": 6195, "mtime": 1752078878057, "results": "347", "hashOfConfig": "334"}, {"size": 8770, "mtime": 1752078878057, "results": "348", "hashOfConfig": "334"}, {"size": 17174, "mtime": 1753606395636, "results": "349", "hashOfConfig": "334"}, {"size": 2174, "mtime": 1752078878057, "results": "350", "hashOfConfig": "334"}, {"size": 1283, "mtime": 1752078878041, "results": "351", "hashOfConfig": "334"}, {"size": 1368, "mtime": 1752677492916, "results": "352", "hashOfConfig": "334"}, {"size": 8520, "mtime": 1753101911257, "results": "353", "hashOfConfig": "334"}, {"size": 4430, "mtime": 1752078878057, "results": "354", "hashOfConfig": "334"}, {"size": 4617, "mtime": 1752078878073, "results": "355", "hashOfConfig": "334"}, {"size": 11891, "mtime": 1752078878073, "results": "356", "hashOfConfig": "334"}, {"size": 10501, "mtime": 1752078878073, "results": "357", "hashOfConfig": "334"}, {"size": 24989, "mtime": 1753436913440, "results": "358", "hashOfConfig": "334"}, {"size": 6509, "mtime": 1753462415834, "results": "359", "hashOfConfig": "334"}, {"size": 9389, "mtime": 1753462468689, "results": "360", "hashOfConfig": "334"}, {"size": 4869, "mtime": 1752078878073, "results": "361", "hashOfConfig": "334"}, {"size": 8673, "mtime": 1752078878073, "results": "362", "hashOfConfig": "334"}, {"size": 1130, "mtime": 1752078878073, "results": "363", "hashOfConfig": "334"}, {"size": 942, "mtime": 1752078878073, "results": "364", "hashOfConfig": "334"}, {"size": 3399, "mtime": 1752078878073, "results": "365", "hashOfConfig": "334"}, {"size": 919, "mtime": 1752078878073, "results": "366", "hashOfConfig": "334"}, {"size": 4801, "mtime": 1752078878073, "results": "367", "hashOfConfig": "334"}, {"size": 6175, "mtime": 1752078878041, "results": "368", "hashOfConfig": "334"}, {"size": 737, "mtime": 1752078878041, "results": "369", "hashOfConfig": "334"}, {"size": 203, "mtime": 1752078878088, "results": "370", "hashOfConfig": "334"}, {"size": 170, "mtime": 1752078878088, "results": "371", "hashOfConfig": "334"}, {"size": 203, "mtime": 1752078878088, "results": "372", "hashOfConfig": "334"}, {"size": 416, "mtime": 1752078878088, "results": "373", "hashOfConfig": "334"}, {"size": 246, "mtime": 1752078878088, "results": "374", "hashOfConfig": "334"}, {"size": 226, "mtime": 1752078878088, "results": "375", "hashOfConfig": "334"}, {"size": 219, "mtime": 1752078878104, "results": "376", "hashOfConfig": "334"}, {"size": 3552, "mtime": 1753436913440, "results": "377", "hashOfConfig": "334"}, {"size": 15373, "mtime": 1753634050009, "results": "378", "hashOfConfig": "334"}, {"size": 18465, "mtime": 1753633524834, "results": "379", "hashOfConfig": "334"}, {"size": 3413, "mtime": 1753436913447, "results": "380", "hashOfConfig": "334"}, {"size": 10429, "mtime": 1753633926321, "results": "381", "hashOfConfig": "334"}, {"size": 13902, "mtime": 1753633333422, "results": "382", "hashOfConfig": "334"}, {"size": 4745, "mtime": 1752078878119, "results": "383", "hashOfConfig": "334"}, {"size": 853, "mtime": 1752078878119, "results": "384", "hashOfConfig": "334"}, {"size": 25246, "mtime": 1754048973753, "results": "385", "hashOfConfig": "334"}, {"size": 10697, "mtime": 1753794777660, "results": "386", "hashOfConfig": "334"}, {"size": 8134, "mtime": 1752078878135, "results": "387", "hashOfConfig": "334"}, {"size": 17474, "mtime": 1752078878135, "results": "388", "hashOfConfig": "334"}, {"size": 11033, "mtime": 1752078878135, "results": "389", "hashOfConfig": "334"}, {"size": 16755, "mtime": 1753794777665, "results": "390", "hashOfConfig": "334"}, {"size": 2781, "mtime": 1752078878142, "results": "391", "hashOfConfig": "334"}, {"size": 3905, "mtime": 1752078878142, "results": "392", "hashOfConfig": "334"}, {"size": 582, "mtime": 1752078878041, "results": "393", "hashOfConfig": "334"}, {"size": 859, "mtime": 1752078878041, "results": "394", "hashOfConfig": "334"}, {"size": 1509, "mtime": 1752078878142, "results": "395", "hashOfConfig": "334"}, {"size": 1485, "mtime": 1752078878142, "results": "396", "hashOfConfig": "334"}, {"size": 5700, "mtime": 1752078878151, "results": "397", "hashOfConfig": "334"}, {"size": 9064, "mtime": 1752652596805, "results": "398", "hashOfConfig": "334"}, {"size": 622, "mtime": 1752078878168, "results": "399", "hashOfConfig": "334"}, {"size": 8493, "mtime": 1752078878151, "results": "400", "hashOfConfig": "334"}, {"size": 2168, "mtime": 1752078878151, "results": "401", "hashOfConfig": "334"}, {"size": 5309, "mtime": 1753452545870, "results": "402", "hashOfConfig": "334"}, {"size": 1110, "mtime": 1753452761979, "results": "403", "hashOfConfig": "334"}, {"size": 8171, "mtime": 1752078878168, "results": "404", "hashOfConfig": "334"}, {"size": 5648, "mtime": 1753480143662, "results": "405", "hashOfConfig": "334"}, {"size": 5403, "mtime": 1753480079226, "results": "406", "hashOfConfig": "334"}, {"size": 7063, "mtime": 1753513187417, "results": "407", "hashOfConfig": "334"}, {"size": 7343, "mtime": 1753607098251, "results": "408", "hashOfConfig": "334"}, {"size": 34339, "mtime": 1753101911271, "results": "409", "hashOfConfig": "334"}, {"size": 12556, "mtime": 1753101911271, "results": "410", "hashOfConfig": "334"}, {"size": 7541, "mtime": 1752083287996, "results": "411", "hashOfConfig": "334"}, {"size": 29492, "mtime": 1753464124502, "results": "412", "hashOfConfig": "334"}, {"size": 8103, "mtime": 1753101911271, "results": "413", "hashOfConfig": "334"}, {"size": 8067, "mtime": 1754035099742, "results": "414", "hashOfConfig": "334"}, {"size": 10266, "mtime": 1753101911271, "results": "415", "hashOfConfig": "334"}, {"size": 9194, "mtime": 1752773304153, "results": "416", "hashOfConfig": "334"}, {"size": 9143, "mtime": 1752773218942, "results": "417", "hashOfConfig": "334"}, {"size": 11142, "mtime": 1752772799158, "results": "418", "hashOfConfig": "334"}, {"size": 11553, "mtime": 1752773033163, "results": "419", "hashOfConfig": "334"}, {"size": 13549, "mtime": 1752774443482, "results": "420", "hashOfConfig": "334"}, {"size": 9759, "mtime": 1752773120405, "results": "421", "hashOfConfig": "334"}, {"size": 8299, "mtime": 1752772955122, "results": "422", "hashOfConfig": "334"}, {"size": 1368, "mtime": 1753101911271, "results": "423", "hashOfConfig": "334"}, {"size": 4977, "mtime": 1753794777665, "results": "424", "hashOfConfig": "334"}, {"size": 3891, "mtime": 1753794777665, "results": "425", "hashOfConfig": "334"}, {"size": 5245, "mtime": 1753794777670, "results": "426", "hashOfConfig": "334"}, {"size": 5891, "mtime": 1753436185891, "results": "427", "hashOfConfig": "334"}, {"size": 25452, "mtime": 1753693959767, "results": "428", "hashOfConfig": "334"}, {"size": 4683, "mtime": 1753794777665, "results": "429", "hashOfConfig": "334"}, {"size": 4248, "mtime": 1753794777665, "results": "430", "hashOfConfig": "334"}, {"size": 6100, "mtime": 1753794777665, "results": "431", "hashOfConfig": "334"}, {"size": 10941, "mtime": 1753436913462, "results": "432", "hashOfConfig": "334"}, {"size": 2793, "mtime": 1752078878247, "results": "433", "hashOfConfig": "334"}, {"size": 2106, "mtime": 1752078878041, "results": "434", "hashOfConfig": "334"}, {"size": 11262, "mtime": 1752078878247, "results": "435", "hashOfConfig": "334"}, {"size": 6314, "mtime": 1752164352608, "results": "436", "hashOfConfig": "334"}, {"size": 6366, "mtime": 1752078878247, "results": "437", "hashOfConfig": "334"}, {"size": 9336, "mtime": 1752771772861, "results": "438", "hashOfConfig": "334"}, {"size": 5984, "mtime": 1752164382084, "results": "439", "hashOfConfig": "334"}, {"size": 4230, "mtime": 1752078878247, "results": "440", "hashOfConfig": "334"}, {"size": 2684, "mtime": 1752078878247, "results": "441", "hashOfConfig": "334"}, {"size": 9968, "mtime": 1753794777670, "results": "442", "hashOfConfig": "334"}, {"size": 1575, "mtime": 1752078878247, "results": "443", "hashOfConfig": "334"}, {"size": 9527, "mtime": 1752078878247, "results": "444", "hashOfConfig": "334"}, {"size": 5403, "mtime": 1753072097102, "results": "445", "hashOfConfig": "334"}, {"size": 11655, "mtime": 1752078878247, "results": "446", "hashOfConfig": "334"}, {"size": 11518, "mtime": 1753101911271, "results": "447", "hashOfConfig": "334"}, {"size": 13102, "mtime": 1752078878247, "results": "448", "hashOfConfig": "334"}, {"size": 11196, "mtime": 1753694885759, "results": "449", "hashOfConfig": "334"}, {"size": 8857, "mtime": 1753797803492, "results": "450", "hashOfConfig": "334"}, {"size": 6242, "mtime": 1753690397307, "results": "451", "hashOfConfig": "334"}, {"size": 7340, "mtime": 1752078878262, "results": "452", "hashOfConfig": "334"}, {"size": 1991, "mtime": 1753694628171, "results": "453", "hashOfConfig": "334"}, {"size": 5037, "mtime": 1752078878262, "results": "454", "hashOfConfig": "334"}, {"size": 2500, "mtime": 1752078878262, "results": "455", "hashOfConfig": "334"}, {"size": 9974, "mtime": 1752078878262, "results": "456", "hashOfConfig": "334"}, {"size": 16789, "mtime": 1752078878262, "results": "457", "hashOfConfig": "334"}, {"size": 6697, "mtime": 1752078878262, "results": "458", "hashOfConfig": "334"}, {"size": 12415, "mtime": 1752078878262, "results": "459", "hashOfConfig": "334"}, {"size": 12074, "mtime": 1752078878262, "results": "460", "hashOfConfig": "334"}, {"size": 14058, "mtime": 1752078878278, "results": "461", "hashOfConfig": "334"}, {"size": 6038, "mtime": 1754034455410, "results": "462", "hashOfConfig": "334"}, {"size": 4276, "mtime": 1752078878278, "results": "463", "hashOfConfig": "334"}, {"size": 3025, "mtime": 1752078878278, "results": "464", "hashOfConfig": "334"}, {"size": 4287, "mtime": 1752078878278, "results": "465", "hashOfConfig": "334"}, {"size": 2417, "mtime": 1752078878278, "results": "466", "hashOfConfig": "334"}, {"size": 17959, "mtime": 1752078878278, "results": "467", "hashOfConfig": "334"}, {"size": 2325, "mtime": 1752078878278, "results": "468", "hashOfConfig": "334"}, {"size": 3529, "mtime": 1752078878278, "results": "469", "hashOfConfig": "334"}, {"size": 135, "mtime": 1752078878278, "results": "470", "hashOfConfig": "334"}, {"size": 2012, "mtime": 1753452375303, "results": "471", "hashOfConfig": "334"}, {"size": 9837, "mtime": 1753072138887, "results": "472", "hashOfConfig": "334"}, {"size": 1300, "mtime": 1752078878278, "results": "473", "hashOfConfig": "334"}, {"size": 2137, "mtime": 1752078878293, "results": "474", "hashOfConfig": "334"}, {"size": 2044, "mtime": 1752078878293, "results": "475", "hashOfConfig": "334"}, {"size": 4100, "mtime": 1752088071254, "results": "476", "hashOfConfig": "334"}, {"size": 2897, "mtime": 1752078878293, "results": "477", "hashOfConfig": "334"}, {"size": 4752, "mtime": 1752925041796, "results": "478", "hashOfConfig": "334"}, {"size": 8720, "mtime": 1752925041797, "results": "479", "hashOfConfig": "334"}, {"size": 2505, "mtime": 1752078878293, "results": "480", "hashOfConfig": "334"}, {"size": 6483, "mtime": 1752078878293, "results": "481", "hashOfConfig": "334"}, {"size": 4310, "mtime": 1752078878293, "results": "482", "hashOfConfig": "334"}, {"size": 4919, "mtime": 1752925041797, "results": "483", "hashOfConfig": "334"}, {"size": 803, "mtime": 1752078878041, "results": "484", "hashOfConfig": "334"}, {"size": 486, "mtime": 1752078878041, "results": "485", "hashOfConfig": "334"}, {"size": 8070, "mtime": 1753452392438, "results": "486", "hashOfConfig": "334"}, {"size": 1165, "mtime": 1752078878293, "results": "487", "hashOfConfig": "334"}, {"size": 8019, "mtime": 1752078878309, "results": "488", "hashOfConfig": "334"}, {"size": 2681, "mtime": 1752508805201, "results": "489", "hashOfConfig": "334"}, {"size": 146, "mtime": 1752078878342, "results": "490", "hashOfConfig": "334"}, {"size": 3070, "mtime": 1752078878309, "results": "491", "hashOfConfig": "334"}, {"size": 6869, "mtime": 1752078878325, "results": "492", "hashOfConfig": "334"}, {"size": 2437, "mtime": 1752509552039, "results": "493", "hashOfConfig": "334"}, {"size": 6521, "mtime": 1752078878325, "results": "494", "hashOfConfig": "334"}, {"size": 8512, "mtime": 1752078878325, "results": "495", "hashOfConfig": "334"}, {"size": 6960, "mtime": 1752078878325, "results": "496", "hashOfConfig": "334"}, {"size": 191, "mtime": 1752078878342, "results": "497", "hashOfConfig": "334"}, {"size": 8704, "mtime": 1752078878325, "results": "498", "hashOfConfig": "334"}, {"size": 154, "mtime": 1752078878342, "results": "499", "hashOfConfig": "334"}, {"size": 1212, "mtime": 1752509267083, "results": "500", "hashOfConfig": "334"}, {"size": 630, "mtime": 1752078878325, "results": "501", "hashOfConfig": "334"}, {"size": 1463, "mtime": 1752078878325, "results": "502", "hashOfConfig": "334"}, {"size": 164, "mtime": 1752078878342, "results": "503", "hashOfConfig": "334"}, {"size": 5488, "mtime": 1752078878325, "results": "504", "hashOfConfig": "334"}, {"size": 5597, "mtime": 1752673608938, "results": "505", "hashOfConfig": "334"}, {"size": 182, "mtime": 1752078878356, "results": "506", "hashOfConfig": "334"}, {"size": 4297, "mtime": 1753436913462, "results": "507", "hashOfConfig": "334"}, {"size": 4954, "mtime": 1752078878325, "results": "508", "hashOfConfig": "334"}, {"size": 103, "mtime": 1752078878356, "results": "509", "hashOfConfig": "334"}, {"size": 118, "mtime": 1752078878356, "results": "510", "hashOfConfig": "334"}, {"size": 2218, "mtime": 1752078878340, "results": "511", "hashOfConfig": "334"}, {"size": 5642, "mtime": 1752769171557, "results": "512", "hashOfConfig": "334"}, {"size": 6418, "mtime": 1752078878340, "results": "513", "hashOfConfig": "334"}, {"size": 4561, "mtime": 1752078878342, "results": "514", "hashOfConfig": "334"}, {"size": 3124, "mtime": 1752078878342, "results": "515", "hashOfConfig": "334"}, {"size": 3747, "mtime": 1752228732395, "results": "516", "hashOfConfig": "334"}, {"size": 17543, "mtime": 1752761198324, "results": "517", "hashOfConfig": "334"}, {"size": 2219, "mtime": 1752078878342, "results": "518", "hashOfConfig": "334"}, {"size": 566, "mtime": 1752078878342, "results": "519", "hashOfConfig": "334"}, {"size": 165, "mtime": 1752078878342, "results": "520", "hashOfConfig": "334"}, {"size": 5248, "mtime": 1752078878342, "results": "521", "hashOfConfig": "334"}, {"size": 8082, "mtime": 1752078878342, "results": "522", "hashOfConfig": "334"}, {"size": 2533, "mtime": 1753972819768, "results": "523", "hashOfConfig": "334"}, {"size": 962, "mtime": 1752078878360, "results": "524", "hashOfConfig": "334"}, {"size": 3021, "mtime": 1752078878360, "results": "525", "hashOfConfig": "334"}, {"size": 17737, "mtime": 1752155050122, "results": "526", "hashOfConfig": "334"}, {"size": 11758, "mtime": 1753436913472, "results": "527", "hashOfConfig": "334"}, {"size": 20619, "mtime": 1753463191735, "results": "528", "hashOfConfig": "334"}, {"size": 5076, "mtime": 1753452257285, "results": "529", "hashOfConfig": "334"}, {"size": 7415, "mtime": 1752078878360, "results": "530", "hashOfConfig": "334"}, {"size": 8309, "mtime": 1752078878360, "results": "531", "hashOfConfig": "334"}, {"size": 3053, "mtime": 1752078878360, "results": "532", "hashOfConfig": "334"}, {"size": 1882, "mtime": 1752078878373, "results": "533", "hashOfConfig": "334"}, {"size": 4833, "mtime": 1752078878373, "results": "534", "hashOfConfig": "334"}, {"size": 8177, "mtime": 1753101911271, "results": "535", "hashOfConfig": "334"}, {"size": 2162, "mtime": 1752078878373, "results": "536", "hashOfConfig": "334"}, {"size": 4524, "mtime": 1752078878373, "results": "537", "hashOfConfig": "334"}, {"size": 8324, "mtime": 1753480532798, "results": "538", "hashOfConfig": "334"}, {"size": 4309, "mtime": 1753436913475, "results": "539", "hashOfConfig": "334"}, {"size": 224, "mtime": 1752078878373, "results": "540", "hashOfConfig": "334"}, {"size": 501, "mtime": 1752078878373, "results": "541", "hashOfConfig": "334"}, {"size": 1272, "mtime": 1752078878373, "results": "542", "hashOfConfig": "334"}, {"size": 1703, "mtime": 1752078878373, "results": "543", "hashOfConfig": "334"}, {"size": 4891, "mtime": 1752078878373, "results": "544", "hashOfConfig": "334"}, {"size": 1849, "mtime": 1752078878388, "results": "545", "hashOfConfig": "334"}, {"size": 4287, "mtime": 1753691266907, "results": "546", "hashOfConfig": "334"}, {"size": 2447, "mtime": 1753436913476, "results": "547", "hashOfConfig": "334"}, {"size": 2412, "mtime": 1752078878388, "results": "548", "hashOfConfig": "334"}, {"size": 3949, "mtime": 1752078878388, "results": "549", "hashOfConfig": "334"}, {"size": 2356, "mtime": 1752078878388, "results": "550", "hashOfConfig": "334"}, {"size": 565, "mtime": 1752078878388, "results": "551", "hashOfConfig": "334"}, {"size": 6189, "mtime": 1753464183454, "results": "552", "hashOfConfig": "334"}, {"size": 17713, "mtime": 1753479243478, "results": "553", "hashOfConfig": "334"}, {"size": 170, "mtime": 1752078878388, "results": "554", "hashOfConfig": "334"}, {"size": 22413, "mtime": 1753479696083, "results": "555", "hashOfConfig": "334"}, {"size": 14946, "mtime": 1753479752891, "results": "556", "hashOfConfig": "334"}, {"size": 2980, "mtime": 1753463285109, "results": "557", "hashOfConfig": "334"}, {"size": 1671, "mtime": 1752078878404, "results": "558", "hashOfConfig": "334"}, {"size": 13486, "mtime": 1753480559984, "results": "559", "hashOfConfig": "334"}, {"size": 0, "mtime": 1752078878404, "results": "560", "hashOfConfig": "334"}, {"size": 12996, "mtime": 1752078878404, "results": "561", "hashOfConfig": "334"}, {"size": 410, "mtime": 1752078878404, "results": "562", "hashOfConfig": "334"}, {"size": 5581, "mtime": 1753072256601, "results": "563", "hashOfConfig": "334"}, {"size": 902, "mtime": 1752078878404, "results": "564", "hashOfConfig": "334"}, {"size": 393, "mtime": 1752078878404, "results": "565", "hashOfConfig": "334"}, {"size": 7904, "mtime": 1753616706921, "results": "566", "hashOfConfig": "334"}, {"size": 315, "mtime": 1752078878421, "results": "567", "hashOfConfig": "334"}, {"size": 642, "mtime": 1753794777670, "results": "568", "hashOfConfig": "334"}, {"size": 781, "mtime": 1753691508846, "results": "569", "hashOfConfig": "334"}, {"size": 303, "mtime": 1752078878421, "results": "570", "hashOfConfig": "334"}, {"size": 422, "mtime": 1752078878421, "results": "571", "hashOfConfig": "334"}, {"size": 4903, "mtime": 1753436913484, "results": "572", "hashOfConfig": "334"}, {"size": 8418, "mtime": 1752078878421, "results": "573", "hashOfConfig": "334"}, {"size": 4141, "mtime": 1753436913484, "results": "574", "hashOfConfig": "334"}, {"size": 4768, "mtime": 1752078878421, "results": "575", "hashOfConfig": "334"}, {"size": 12056, "mtime": 1753069833441, "results": "576", "hashOfConfig": "334"}, {"size": 3715, "mtime": 1752078878436, "results": "577", "hashOfConfig": "334"}, {"size": 5950, "mtime": 1752929862955, "results": "578", "hashOfConfig": "334"}, {"size": 9417, "mtime": 1752929450701, "results": "579", "hashOfConfig": "334"}, {"size": 141, "mtime": 1752078878442, "results": "580", "hashOfConfig": "334"}, {"size": 10403, "mtime": 1752078878442, "results": "581", "hashOfConfig": "334"}, {"size": 4149, "mtime": 1752078878442, "results": "582", "hashOfConfig": "334"}, {"size": 7483, "mtime": 1752078878442, "results": "583", "hashOfConfig": "334"}, {"size": 5633, "mtime": 1752078878442, "results": "584", "hashOfConfig": "334"}, {"size": 2746, "mtime": 1752078878442, "results": "585", "hashOfConfig": "334"}, {"size": 8387, "mtime": 1752155050123, "results": "586", "hashOfConfig": "334"}, {"size": 732, "mtime": 1752078878442, "results": "587", "hashOfConfig": "334"}, {"size": 6262, "mtime": 1752078878452, "results": "588", "hashOfConfig": "334"}, {"size": 1732, "mtime": 1753452149675, "results": "589", "hashOfConfig": "334"}, {"size": 3162, "mtime": 1752078878452, "results": "590", "hashOfConfig": "334"}, {"size": 4293, "mtime": 1752078878452, "results": "591", "hashOfConfig": "334"}, {"size": 1829, "mtime": 1752078878452, "results": "592", "hashOfConfig": "334"}, {"size": 1339, "mtime": 1752078878452, "results": "593", "hashOfConfig": "334"}, {"size": 2395, "mtime": 1753072301980, "results": "594", "hashOfConfig": "334"}, {"size": 8254, "mtime": 1753691713892, "results": "595", "hashOfConfig": "334"}, {"size": 10723, "mtime": 1752435112746, "results": "596", "hashOfConfig": "334"}, {"size": 27805, "mtime": 1753797803491, "results": "597", "hashOfConfig": "334"}, {"size": 26307, "mtime": 1753452543994, "results": "598", "hashOfConfig": "334"}, {"size": 2617, "mtime": 1753436913369, "results": "599", "hashOfConfig": "334"}, {"size": 2092, "mtime": 1752078877815, "results": "600", "hashOfConfig": "334"}, {"size": 3430, "mtime": 1752078877815, "results": "601", "hashOfConfig": "334"}, {"size": 7332, "mtime": 1753436913385, "results": "602", "hashOfConfig": "334"}, {"size": 2476, "mtime": 1752163515003, "results": "603", "hashOfConfig": "334"}, {"size": 16234, "mtime": 1753451951646, "results": "604", "hashOfConfig": "334"}, {"size": 2523, "mtime": 1752652231768, "results": "605", "hashOfConfig": "334"}, {"size": 5217, "mtime": 1752078877820, "results": "606", "hashOfConfig": "334"}, {"size": 7075, "mtime": 1753436913385, "results": "607", "hashOfConfig": "334"}, {"size": 7555, "mtime": 1753436913385, "results": "608", "hashOfConfig": "334"}, {"size": 6825, "mtime": 1752078877824, "results": "609", "hashOfConfig": "334"}, {"size": 7792, "mtime": 1752078877828, "results": "610", "hashOfConfig": "334"}, {"size": 6973, "mtime": 1752078877829, "results": "611", "hashOfConfig": "334"}, {"size": 2892, "mtime": 1752078877829, "results": "612", "hashOfConfig": "334"}, {"size": 6477, "mtime": 1752078877830, "results": "613", "hashOfConfig": "334"}, {"size": 15800, "mtime": 1753436913385, "results": "614", "hashOfConfig": "334"}, {"size": 7803, "mtime": 1752078877832, "results": "615", "hashOfConfig": "334"}, {"size": 2313, "mtime": 1752078877833, "results": "616", "hashOfConfig": "334"}, {"size": 5468, "mtime": 1752078877833, "results": "617", "hashOfConfig": "334"}, {"size": 2609, "mtime": 1752078877834, "results": "618", "hashOfConfig": "334"}, {"size": 4654, "mtime": 1752652313957, "results": "619", "hashOfConfig": "334"}, {"size": 760, "mtime": 1752078877813, "results": "620", "hashOfConfig": "334"}, {"size": 21125, "mtime": 1752078877837, "results": "621", "hashOfConfig": "334"}, {"size": 6062, "mtime": 1752078877838, "results": "622", "hashOfConfig": "334"}, {"size": 17024, "mtime": 1752435156823, "results": "623", "hashOfConfig": "334"}, {"size": 16748, "mtime": 1752078877838, "results": "624", "hashOfConfig": "334"}, {"size": 6083, "mtime": 1753436913393, "results": "625", "hashOfConfig": "334"}, {"size": 4878, "mtime": 1752078877841, "results": "626", "hashOfConfig": "334"}, {"size": 4770, "mtime": 1752078877841, "results": "627", "hashOfConfig": "334"}, {"size": 1410, "mtime": 1752078877841, "results": "628", "hashOfConfig": "334"}, {"size": 791, "mtime": 1752860591670, "results": "629", "hashOfConfig": "334"}, {"size": 5323, "mtime": 1753452346595, "results": "630", "hashOfConfig": "334"}, {"size": 1022, "mtime": 1752078877852, "results": "631", "hashOfConfig": "334"}, {"size": 2650, "mtime": 1752078877852, "results": "632", "hashOfConfig": "334"}, {"size": 26953, "mtime": 1753436913395, "results": "633", "hashOfConfig": "334"}, {"size": 6236, "mtime": 1753436185859, "results": "634", "hashOfConfig": "334"}, {"size": 6726, "mtime": 1752599290194, "results": "635", "hashOfConfig": "334"}, {"size": 4491, "mtime": 1753436913385, "results": "636", "hashOfConfig": "334"}, {"size": 7708, "mtime": 1753625784237, "results": "637", "hashOfConfig": "334"}, {"size": 5954, "mtime": 1752758810126, "results": "638", "hashOfConfig": "334"}, {"size": 2112, "mtime": 1752771048245, "results": "639", "hashOfConfig": "334"}, {"size": 5706, "mtime": 1752926536533, "results": "640", "hashOfConfig": "334"}, {"size": 6994, "mtime": 1752761122820, "results": "641", "hashOfConfig": "334"}, {"size": 1570, "mtime": 1752776247399, "results": "642", "hashOfConfig": "334"}, {"size": 3452, "mtime": 1753436913462, "results": "643", "hashOfConfig": "334"}, {"size": 143, "mtime": 1752750980930, "results": "644", "hashOfConfig": "334"}, {"size": 8146, "mtime": 1753480209077, "results": "645", "hashOfConfig": "334"}, {"size": 5729, "mtime": 1753513257714, "results": "646", "hashOfConfig": "334"}, {"size": 4592, "mtime": 1753513088213, "results": "647", "hashOfConfig": "334"}, {"size": 5356, "mtime": 1753513381353, "results": "648", "hashOfConfig": "334"}, {"size": 6750, "mtime": 1753513320288, "results": "649", "hashOfConfig": "334"}, {"size": 2039, "mtime": 1753481558040, "results": "650", "hashOfConfig": "334"}, {"size": 2030, "mtime": 1753481520324, "results": "651", "hashOfConfig": "334"}, {"size": 2863, "mtime": 1753481585882, "results": "652", "hashOfConfig": "334"}, {"size": 3781, "mtime": 1754046779183, "results": "653", "hashOfConfig": "334"}, {"size": 760, "mtime": 1753479270975, "results": "654", "hashOfConfig": "334"}, {"size": 62143, "mtime": 1754049704011, "results": "655", "hashOfConfig": "334"}, {"size": 6675, "mtime": 1753512955858, "results": "656", "hashOfConfig": "334"}, {"size": 1013, "mtime": 1753633110499, "results": "657", "hashOfConfig": "334"}, {"size": 7437, "mtime": 1754048441063, "results": "658", "hashOfConfig": "334"}, {"size": 15246, "mtime": 1754050346073, "results": "659", "hashOfConfig": "334"}, {"size": 8450, "mtime": 1754050360573, "results": "660", "hashOfConfig": "334"}, {"size": 8702, "mtime": 1754050374137, "results": "661", "hashOfConfig": "334"}, {"size": 7212, "mtime": 1754048394135, "results": "662", "hashOfConfig": "334"}, {"size": 13622, "mtime": 1754050259711, "results": "663", "hashOfConfig": "334"}, {"size": 4239, "mtime": 1754050453224, "results": "664", "hashOfConfig": "334"}, {"size": 2443, "mtime": 1754046915702, "results": "665", "hashOfConfig": "334"}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h3uczq", {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1533", "messages": "1534", "suppressedMessages": "1535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1536", "messages": "1537", "suppressedMessages": "1538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1539", "messages": "1540", "suppressedMessages": "1541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1542", "messages": "1543", "suppressedMessages": "1544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1545", "messages": "1546", "suppressedMessages": "1547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1548", "messages": "1549", "suppressedMessages": "1550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1551", "messages": "1552", "suppressedMessages": "1553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1554", "messages": "1555", "suppressedMessages": "1556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1557", "messages": "1558", "suppressedMessages": "1559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1560", "messages": "1561", "suppressedMessages": "1562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1563", "messages": "1564", "suppressedMessages": "1565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1566", "messages": "1567", "suppressedMessages": "1568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1569", "messages": "1570", "suppressedMessages": "1571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1572", "messages": "1573", "suppressedMessages": "1574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1575", "messages": "1576", "suppressedMessages": "1577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1578", "messages": "1579", "suppressedMessages": "1580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1581", "messages": "1582", "suppressedMessages": "1583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1584", "messages": "1585", "suppressedMessages": "1586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1587", "messages": "1588", "suppressedMessages": "1589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1590", "messages": "1591", "suppressedMessages": "1592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1593", "messages": "1594", "suppressedMessages": "1595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1596", "messages": "1597", "suppressedMessages": "1598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1599", "messages": "1600", "suppressedMessages": "1601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1602", "messages": "1603", "suppressedMessages": "1604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1605", "messages": "1606", "suppressedMessages": "1607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1608", "messages": "1609", "suppressedMessages": "1610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1611", "messages": "1612", "suppressedMessages": "1613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1614", "messages": "1615", "suppressedMessages": "1616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1617", "messages": "1618", "suppressedMessages": "1619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1620", "messages": "1621", "suppressedMessages": "1622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1623", "messages": "1624", "suppressedMessages": "1625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1626", "messages": "1627", "suppressedMessages": "1628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1629", "messages": "1630", "suppressedMessages": "1631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1632", "messages": "1633", "suppressedMessages": "1634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1635", "messages": "1636", "suppressedMessages": "1637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1638", "messages": "1639", "suppressedMessages": "1640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1641", "messages": "1642", "suppressedMessages": "1643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1644", "messages": "1645", "suppressedMessages": "1646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1647", "messages": "1648", "suppressedMessages": "1649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1650", "messages": "1651", "suppressedMessages": "1652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1653", "messages": "1654", "suppressedMessages": "1655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1656", "messages": "1657", "suppressedMessages": "1658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1659", "messages": "1660", "suppressedMessages": "1661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard-app\\src\\components\\ads\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\AuthGuard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\AboutTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ActivityItem.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessProfileStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\BusinessStats.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\FullScreenImageViewer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\NotificationsModalNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\QRCodeDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\business\\TabNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\Collapsible.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\common\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\BusinessCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CategorySelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\CompactLocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\DiscoverySkeletons.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ErrorComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\FullScreenLocationSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\NavigationHandlers.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ResultsList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SearchSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\SortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\CompactLocationPickerStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\DiscoverScreenStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\LocationSelectorScreenStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\styles\\ResultsListStyles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\discovery\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ExternalLink.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\auth\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\business\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\customer\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\posts\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\products\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\features\\shared\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\BusinessPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostCreator.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostEditModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\CustomerPostModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedFilters.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\FeedHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostCard.tsx", ["1662", "1663", "1664"], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostOptionsBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\PostSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\ProductSelectorModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\feed\\UnifiedFeedList.tsx", [], ["1665", "1666"], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\forms\\FormPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HapticTab.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\HelloWave.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\AuthContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\DashboardContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\OnboardingContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\SafeAreaWrapper.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\ScreenContainer.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\layout\\StatusBarManager.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\metrics\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessFollowersModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessLikesModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowersList.tsx", [], ["1667", "1668"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesList.tsx", [], ["1669", "1670"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsList.tsx", [], ["1671", "1672"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\ProductsSortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\VariantList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageCardModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ManageProductsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AdvancedFeaturesSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\SocialLinksSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\sections\\StatusSettingsSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\VariantModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\FollowingList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\LikesList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\components\\ReviewsSortBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\EditProfileModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\FollowingModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\LikesModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\customer\\ReviewsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\notifications\\NotificationPreferences.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\onboarding\\BusinessDetailsContent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ParallaxScrollView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\CategoryBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ColorPickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\ImagePickerBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\LocalityBottomSheetPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\pickers\\VariantTypeBottomSheet.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\post\\SinglePostScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\CollapsibleDescription.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ImageCarousel.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\product\\VariantSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ActivityCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AddressInformationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\AvatarUploadWithCrop.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\PersonalInformationSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\providers\\AlertProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScanner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\qr\\QRScannerModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\EmailLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PasswordManagementSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\settings\\PhoneLinkingSection.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\layout\\DashboardLayout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\BottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\DrawerProvider.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\navigation\\UnifiedBottomNavigation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\NotificationIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\screens\\DiscoverScreenNew.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\EmptyState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\Header.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ProductCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\FollowingModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\LikesModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ProductsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\ReviewsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SearchComponent.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SkeletonLoaders.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SortSelector.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\social\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedText.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ThemedView.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AlertDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AnimatedLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Button.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\buttons\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ComingSoonModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\DukancardLogo.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorBoundary.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorRecovery.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ErrorState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\feedback\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\FormField.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\forms\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\GoogleIcon.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\IconSymbol.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\InlineErrorHandler.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Input.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\inputs\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LocationPicker.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\modals\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\navigation\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OfflineComponents.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\OTPInput.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ProductSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RetryButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ReviewSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\RoleCard.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SkeletonLoader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\SplashScreen.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.ios.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\TabBarBackground.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\ThemeToggleButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\Toast.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\config\\publicKeys.ts", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\Colors.ts", [], [], "C:\\web-app\\dukancard-app\\src\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\AuthContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\DiscoveryContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\LocationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\NotificationContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\OnboardingContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\use-mobile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAlert.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthErrorHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAuthRefresh.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessCardData.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useBusinessInteractions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useColorScheme.web.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useDebounce.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useDynamicSafeArea.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLoadingState.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useLocationPermission.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSinglePost.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useSlugValidation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useTheme.ts", [], [], "C:\\web-app\\dukancard-app\\src\\hooks\\useThemeColor.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\businessActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\DiscoveryService.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\locationActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\productActions.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\types.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\locationUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\secureBusinessProfiles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\discovery\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard-app\\src\\services\\locationStorageService.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ad.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\auth.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\business\\analytics.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\components.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\discovery.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\profile.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\screens.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\ui.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\apiClient.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\deletePostMedia.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\distanceCalculation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\errorHandling.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\galleryLimits.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\imageCompression.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\index.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\navigation.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\networkStatus.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\toast.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\userProfileUtils.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\validationSchemas.ts", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\choose-role.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\complete-profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\login.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\analytics.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\customers.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\products.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\business\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\favorites.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\notifications.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\profile.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\PasswordUpdateSection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\customer\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(dashboard)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\address-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\business-details.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\card-information.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\plan-selection.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(onboarding)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\explore.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(tabs)\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\+not-found.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\business\\[businessSlug].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\index.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\post\\[postId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\product\\[productId].tsx", [], [], "C:\\web-app\\dukancard-app\\app\\_layout.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step1AvatarName.tsx", [], [], "C:\\web-app\\dukancard-app\\app\\(auth)\\components\\Step2AddressLocation.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessDeleteAccountModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessStatusSettingsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\GalleryModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\ShareBusinessCardModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.styles.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ProfileHeader.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\types\\gallery.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\BusinessReviewsModal.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessFollowingList.tsx", [], ["1673", "1674"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessLikesGivenList.tsx", [], ["1675", "1676"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsGivenList.tsx", [], ["1677", "1678"], "C:\\web-app\\dukancard-app\\src\\components\\modals\\business\\components\\BusinessReviewsList.tsx", [], ["1679"], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessFollowersModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessLikesModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\skeletons\\modals\\BusinessReviewsModalSkeleton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\config\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\business.ts", [], [], "C:\\web-app\\dukancard-app\\src\\types\\supabase.ts", [], [], "C:\\web-app\\dukancard-app\\src\\components\\shared\\ui\\SocialEmptyState.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\PostDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentBottomSheet.tsx", ["1680"], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentDisplay.tsx", ["1681"], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentInput.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\CommentSection.tsx", ["1682", "1683", "1684"], [], "C:\\web-app\\dukancard-app\\src\\components\\ui\\LikeButton.tsx", [], [], "C:\\web-app\\dukancard-app\\src\\stores\\likeCommentStore.ts", [], [], "C:\\web-app\\dukancard-app\\src\\stores\\simpleLikeCommentStore.ts", [], [], "C:\\web-app\\dukancard-app\\src\\utils\\formatNumber.ts", [], [], {"ruleId": "1685", "severity": 1, "message": "1686", "line": 31, "column": 8, "nodeType": "1687", "endLine": 31, "endColumn": 18}, {"ruleId": "1685", "severity": 1, "message": "1688", "line": 32, "column": 8, "nodeType": "1687", "endLine": 32, "endColumn": 26}, {"ruleId": "1685", "severity": 1, "message": "1689", "line": 33, "column": 8, "nodeType": "1687", "endLine": 33, "endColumn": 22}, {"ruleId": "1690", "severity": 1, "message": "1691", "line": 404, "column": 6, "nodeType": "1692", "endLine": 404, "endColumn": 56, "suggestions": "1693", "suppressions": "1694"}, {"ruleId": "1690", "severity": 1, "message": "1695", "line": 424, "column": 6, "nodeType": "1692", "endLine": 424, "endColumn": 69, "suggestions": "1696", "suppressions": "1697"}, {"ruleId": "1690", "severity": 1, "message": "1698", "line": 89, "column": 6, "nodeType": "1692", "endLine": 89, "endColumn": 30, "suggestions": "1699", "suppressions": "1700"}, {"ruleId": "1690", "severity": 1, "message": "1698", "line": 96, "column": 6, "nodeType": "1692", "endLine": 96, "endColumn": 12, "suggestions": "1701", "suppressions": "1702"}, {"ruleId": "1690", "severity": 1, "message": "1703", "line": 116, "column": 6, "nodeType": "1692", "endLine": 116, "endColumn": 30, "suggestions": "1704", "suppressions": "1705"}, {"ruleId": "1690", "severity": 1, "message": "1703", "line": 123, "column": 6, "nodeType": "1692", "endLine": 123, "endColumn": 12, "suggestions": "1706", "suppressions": "1707"}, {"ruleId": "1690", "severity": 1, "message": "1708", "line": 237, "column": 6, "nodeType": "1692", "endLine": 237, "endColumn": 12, "suggestions": "1709", "suppressions": "1710"}, {"ruleId": "1690", "severity": 1, "message": "1711", "line": 248, "column": 6, "nodeType": "1692", "endLine": 248, "endColumn": 32, "suggestions": "1712", "suppressions": "1713"}, {"ruleId": "1690", "severity": 1, "message": "1714", "line": 93, "column": 6, "nodeType": "1692", "endLine": 93, "endColumn": 24, "suggestions": "1715", "suppressions": "1716"}, {"ruleId": "1690", "severity": 1, "message": "1714", "line": 100, "column": 6, "nodeType": "1692", "endLine": 100, "endColumn": 12, "suggestions": "1717", "suppressions": "1718"}, {"ruleId": "1690", "severity": 1, "message": "1703", "line": 93, "column": 6, "nodeType": "1692", "endLine": 93, "endColumn": 24, "suggestions": "1719", "suppressions": "1720"}, {"ruleId": "1690", "severity": 1, "message": "1703", "line": 100, "column": 6, "nodeType": "1692", "endLine": 100, "endColumn": 12, "suggestions": "1721", "suppressions": "1722"}, {"ruleId": "1690", "severity": 1, "message": "1723", "line": 96, "column": 6, "nodeType": "1692", "endLine": 96, "endColumn": 32, "suggestions": "1724", "suppressions": "1725"}, {"ruleId": "1690", "severity": 1, "message": "1723", "line": 103, "column": 6, "nodeType": "1692", "endLine": 103, "endColumn": 12, "suggestions": "1726", "suppressions": "1727"}, {"ruleId": "1690", "severity": 1, "message": "1723", "line": 173, "column": 6, "nodeType": "1692", "endLine": 173, "endColumn": 38, "suggestions": "1728", "suppressions": "1729"}, {"ruleId": "1690", "severity": 1, "message": "1730", "line": 88, "column": 6, "nodeType": "1692", "endLine": 88, "endColumn": 17, "suggestions": "1731"}, {"ruleId": "1685", "severity": 1, "message": "1732", "line": 14, "column": 8, "nodeType": "1687", "endLine": 14, "endColumn": 20}, {"ruleId": "1685", "severity": 1, "message": "1733", "line": 13, "column": 8, "nodeType": "1687", "endLine": 13, "endColumn": 22}, {"ruleId": "1685", "severity": 1, "message": "1732", "line": 14, "column": 8, "nodeType": "1687", "endLine": 14, "endColumn": 20}, {"ruleId": "1690", "severity": 1, "message": "1734", "line": 47, "column": 9, "nodeType": "1735", "endLine": 47, "endColumn": 48}, "import/no-named-as-default", "Using exported name 'LikeButton' as identifier for default import.", "ImportDefaultSpecifier", "Using exported name 'CommentBottomSheet' as identifier for default import.", "Using exported name 'CommentSection' as identifier for default import.", "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'styles.loadingFooter' and 'styles.loadingText'. Either include them or remove the dependency array.", "ArrayExpression", ["1736"], ["1737"], "React Hook useCallback has missing dependencies: 'isLoading', 'styles.emptyContainer', 'styles.emptySubtitle', and 'styles.emptyTitle'. Either include them or remove the dependency array.", ["1738"], ["1739"], "React Hook useEffect has a missing dependency: 'fetchFollowers'. Either include it or remove the dependency array.", ["1740"], ["1741"], ["1742"], ["1743"], "React Hook useEffect has a missing dependency: 'fetchLikes'. Either include it or remove the dependency array.", ["1744"], ["1745"], ["1746"], ["1747"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["1748"], ["1749"], "React Hook useEffect has missing dependencies: 'fetchProducts' and 'user'. Either include them or remove the dependency array.", ["1750"], ["1751"], "React Hook useEffect has a missing dependency: 'fetchSubscriptions'. Either include it or remove the dependency array.", ["1752"], ["1753"], ["1754"], ["1755"], ["1756"], ["1757"], ["1758"], ["1759"], "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["1760"], ["1761"], ["1762"], ["1763"], ["1764"], ["1765"], "React Hook useEffect has missing dependencies: 'closeBottomSheet' and 'openBottomSheet'. Either include them or remove the dependency array.", ["1766"], "Using exported name 'CommentInput' as identifier for default import.", "Using exported name 'CommentDisplay' as identifier for default import.", "The 'comments' logical expression could make the dependencies of useMemo Hook (at line 88) change on every render. To fix this, wrap the initialization of 'comments' in its own useMemo() Hook.", "VariableDeclarator", {"desc": "1767", "fix": "1768"}, {"kind": "1769", "justification": "1770"}, {"desc": "1771", "fix": "1772"}, {"kind": "1769", "justification": "1770"}, {"desc": "1773", "fix": "1774"}, {"kind": "1769", "justification": "1770"}, {"desc": "1775", "fix": "1776"}, {"kind": "1769", "justification": "1770"}, {"desc": "1777", "fix": "1778"}, {"kind": "1769", "justification": "1770"}, {"desc": "1779", "fix": "1780"}, {"kind": "1769", "justification": "1770"}, {"desc": "1781", "fix": "1782"}, {"kind": "1769", "justification": "1770"}, {"desc": "1783", "fix": "1784"}, {"kind": "1769", "justification": "1770"}, {"desc": "1785", "fix": "1786"}, {"kind": "1769", "justification": "1770"}, {"desc": "1787", "fix": "1788"}, {"kind": "1769", "justification": "1770"}, {"desc": "1789", "fix": "1790"}, {"kind": "1769", "justification": "1770"}, {"desc": "1779", "fix": "1791"}, {"kind": "1769", "justification": "1770"}, {"desc": "1792", "fix": "1793"}, {"kind": "1769", "justification": "1770"}, {"desc": "1794", "fix": "1795"}, {"kind": "1769", "justification": "1770"}, {"desc": "1796", "fix": "1797"}, {"kind": "1769", "justification": "1770"}, {"desc": "1798", "fix": "1799"}, "Update the dependencies array to be: [isLoading, hasMore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", {"range": "1800", "text": "1801"}, "directive", "", "Update the dependencies array to be: [isLoading, posts.length, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", {"range": "1802", "text": "1803"}, "Update the dependencies array to be: [businessId, fetchFollowers, searchTerm]", {"range": "1804", "text": "1805"}, "Update the dependencies array to be: [fetchFollowers, page]", {"range": "1806", "text": "1807"}, "Update the dependencies array to be: [businessId, fetchLikes, searchTerm]", {"range": "1808", "text": "1809"}, "Update the dependencies array to be: [fetchLikes, page]", {"range": "1810", "text": "1811"}, "Update the dependencies array to be: [fetchProducts, user]", {"range": "1812", "text": "1813"}, "Update the dependencies array to be: [activeSearchTerm, fetchProducts, sortBy, user]", {"range": "1814", "text": "1815"}, "Update the dependencies array to be: [user, searchTerm, fetchSubscriptions]", {"range": "1816", "text": "1817"}, "Update the dependencies array to be: [fetchSubscriptions, page]", {"range": "1818", "text": "1819"}, "Update the dependencies array to be: [user, searchTerm, fetchLikes]", {"range": "1820", "text": "1821"}, {"range": "1822", "text": "1811"}, "Update the dependencies array to be: [user, sortBy, searchTerm, fetchReviews]", {"range": "1823", "text": "1824"}, "Update the dependencies array to be: [fetchReviews, page]", {"range": "1825", "text": "1826"}, "Update the dependencies array to be: [businessId, sortBy, searchTerm, fetchReviews]", {"range": "1827", "text": "1828"}, "Update the dependencies array to be: [closeBottomSheet, isVisible, openBottomSheet]", {"range": "1829", "text": "1830"}, [12349, 12399], "[isLoading, has<PERSON>ore, styles.loadingFooter, styles.loadingText, primaryColor, mutedTextColor]", [13003, 13066], "[isLoading, posts.length, styles.emptyContainer, styles.emptyTitle, styles.emptySubtitle, textColor, mutedTextColor, getEmptyStateMessage]", [2764, 2788], "[businessId, fetchFollowers, searchTerm]", [2927, 2933], "[fetch<PERSON><PERSON><PERSON><PERSON>, page]", [3238, 3262], "[businessId, fetchLikes, searchTerm]", [3397, 3403], "[fetchLikes, page]", [7511, 7517], "[fetchProducts, user]", [7809, 7835], "[activeSearchTerm, fetchProducts, sortBy, user]", [2981, 2999], "[user, searchTerm, fetchSubscriptions]", [3142, 3148], "[fetchSubscriptions, page]", [2745, 2763], "[user, searchTerm, fetchLikes]", [2898, 2904], [2853, 2879], "[user, sortBy, searchTerm, fetchReviews]", [3016, 3022], "[fetchR<PERSON>ie<PERSON>, page]", [5178, 5210], "[businessId, sortBy, searchTerm, fetchReviews]", [2354, 2365], "[closeBottomSheet, isVisible, openBottomSheet]"]