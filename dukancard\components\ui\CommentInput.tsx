'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLikeCommentStore } from '@/lib/stores/simpleLikeCommentStore';

interface CommentInputProps {
  postId: string;
  postSource: 'business' | 'customer';
  parentCommentId?: string;
  placeholder?: string;
  maxLength?: number;
  autoFocus?: boolean;
  className?: string;
  onSubmit?: (content: string) => void;
  onCancel?: () => void;
  showCancel?: boolean;
  disabled?: boolean;
}

export function CommentInput({
  postId,
  postSource,
  parentCommentId,
  placeholder = 'Write a comment...',
  maxLength = 500,
  autoFocus = false,
  className,
  onSubmit,
  onCancel,
  showCancel = false,
  disabled = false,
}: CommentInputProps) {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const { createComment } = useLikeCommentStore();

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [content]);

  // Auto-focus if requested
  useEffect(() => {
    if (autoFocus && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [autoFocus]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedContent = content.trim();
    if (!trimmedContent || isSubmitting || disabled) return;

    setIsSubmitting(true);
    
    try {
      const success = await createComment(postId, postSource, trimmedContent, parentCommentId);
      
      if (success) {
        setContent('');
        onSubmit?.(trimmedContent);
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit(e);
    }
    
    if (e.key === 'Escape' && showCancel) {
      onCancel?.();
    }
  };

  const handleCancel = () => {
    setContent('');
    onCancel?.();
  };

  const remainingChars = maxLength - content.length;
  const isOverLimit = remainingChars < 0;
  const isNearLimit = remainingChars <= 50;
  const canSubmit = content.trim().length > 0 && !isOverLimit && !isSubmitting && !disabled;

  return (
    <form onSubmit={handleSubmit} className={cn('space-y-3', className)}>
      <div className="relative">
        {/* Textarea */}
        <textarea
          ref={textareaRef}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled || isSubmitting}
          className={cn(
            'w-full min-h-[80px] max-h-[200px] px-4 py-3 pr-12',
            'border border-gray-200 rounded-lg resize-none',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
            'placeholder:text-gray-400',
            'disabled:bg-gray-50 disabled:cursor-not-allowed',
            isOverLimit && 'border-red-300 focus:ring-red-500',
            'transition-colors duration-200'
          )}
          rows={1}
          maxLength={maxLength + 50} // Allow slight overflow for better UX
        />

        {/* Submit button inside textarea */}
        <button
          type="submit"
          disabled={!canSubmit}
          className={cn(
            'absolute bottom-3 right-3 p-2 rounded-full transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            canSubmit
              ? 'bg-blue-600 text-white hover:bg-blue-700 active:scale-95'
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
          )}
          aria-label="Submit comment"
        >
          {isSubmitting ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Send className="h-4 w-4" />
          )}
        </button>
      </div>

      {/* Footer with character count and actions */}
      <div className="flex items-center justify-between text-sm">
        {/* Character count */}
        <div className={cn(
          'transition-colors duration-200',
          isOverLimit ? 'text-red-600' : isNearLimit ? 'text-amber-600' : 'text-gray-500'
        )}>
          {isNearLimit || isOverLimit ? (
            <span className="font-medium">
              {remainingChars} characters {remainingChars === 1 ? 'remaining' : remainingChars < 0 ? 'over limit' : 'remaining'}
            </span>
          ) : (
            <span>{content.length}/{maxLength}</span>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex items-center gap-2">
          {showCancel && (
            <button
              type="button"
              onClick={handleCancel}
              disabled={isSubmitting}
              className={cn(
                'px-3 py-1 text-gray-600 hover:text-gray-800',
                'focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 rounded',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'transition-colors duration-200'
              )}
            >
              Cancel
            </button>
          )}
          
          <div className="text-gray-400 text-xs">
            Ctrl+Enter to submit
          </div>
        </div>
      </div>

      {/* Error message for over limit */}
      {isOverLimit && (
        <div className="text-red-600 text-sm font-medium">
          Comment is too long. Please shorten it by {Math.abs(remainingChars)} characters.
        </div>
      )}

      {/* Screen reader announcements */}
      <div className="sr-only" aria-live="polite">
        {isSubmitting && 'Submitting comment...'}
        {isOverLimit && `Comment is ${Math.abs(remainingChars)} characters over the limit`}
      </div>
    </form>
  );
}

// Hook for comment input functionality
export function useCommentInput(
  postId: string,
  postSource: 'business' | 'customer',
  parentCommentId?: string
) {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { createComment } = useLikeCommentStore();

  const submitComment = async (commentContent?: string) => {
    const finalContent = (commentContent || content).trim();
    if (!finalContent || isSubmitting) return false;

    setIsSubmitting(true);
    
    try {
      const success = await createComment(postId, postSource, finalContent, parentCommentId);
      
      if (success) {
        setContent('');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error submitting comment:', error);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    content,
    setContent,
    isSubmitting,
    submitComment,
  };
}

export default CommentInput;
