(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/utils/client-image-compression.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_utils_client-image-compression_ts_45be4be3._.js",
  "static/chunks/lib_utils_client-image-compression_ts_9a16d210._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/utils/client-image-compression.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/actions/shared/upload-business-post-media.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_next_headers_e3b563e7.js",
  "static/chunks/_1cac350c._.js",
  "static/chunks/lib_actions_shared_upload-business-post-media_ts_9a16d210._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/shared/upload-business-post-media.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/actions/posts.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_actions_adc7fce3._.js",
  "static/chunks/lib_actions_posts_ts_88bf520b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/posts.ts [app-client] (ecmascript)");
    });
});
}}),
}]);