import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  Animated,
  Linking,
  ActivityIndicator,
  FlatList,
  Modal,
} from "react-native";
import { UnifiedPost } from "@/lib/actions/posts/unifiedFeed";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import {
  Heart,
  Phone,
  MapPin,
  MoreVertical,
  Package,
  User,
  MessageCircle,
  Share2,
} from "lucide-react-native";
import WhatsAppIcon from "@/src/components/icons/WhatsAppIcon";
import {
  sharePost,
  showComments,
} from "@/backend/supabase/services/posts/postInteractions";
import LikeButton from "@/src/components/ui/LikeButton";
import CommentBottomSheet from "@/src/components/ui/CommentBottomSheet";
import CommentSection from "@/src/components/ui/CommentSection";
import {
  fetchProductsByIds,
  ProductData,
} from "@/backend/supabase/services/products/productService";
import { ProductCard } from "@/src/components/shared/ui";
import { usePostOwnership } from "@/src/hooks/usePostOwnership";
import { deleteCustomerPost } from "@/lib/actions/customerPosts";
import { deleteBusinessPost } from "@/lib/actions/businessPosts";
import { CustomerPostEditModal } from "./CustomerPostEditModal";
import { BusinessPostEditModal } from "./BusinessPostEditModal";
import { useRouter } from "expo-router";
import { generatePostPath } from "@/src/utils/postUrl";
import { postCardStyles as styles } from "@/styles/_postCardStyles";
import { AlertDialog } from "@/src/components/ui/AlertDialog";
import { useAlert } from "@/src/hooks/useAlert";
import { useToast } from "@/src/components/ui/Toast";
import { PostDeleteDialog } from "@/src/components/ui/PostDeleteDialog";
// Removed real-time subscription import as per user request

interface PostCardProps {
  post: UnifiedPost;
  index?: number;
  onPostUpdate?: (
    postId: string,
    newContent: string,
    newImageUrl?: string | null
  ) => void;
  onPostDelete?: (postId: string) => void;
  onProductsUpdate?: (postId: string, newProductIds: string[]) => void;
  onMorePress?: (post: UnifiedPost, isOwner: boolean) => void;
  showActualAspectRatio?: boolean;
  disablePostClick?: boolean;
  enableImageFullscreen?: boolean;
}

const { width: screenWidth } = Dimensions.get("window");

export function PostCard({
  post,
  index = 0,
  onPostUpdate,
  onPostDelete,
  onProductsUpdate,
  onMorePress,
  showActualAspectRatio = false,
  disablePostClick = false,
  enableImageFullscreen = false,
}: PostCardProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";
  const router = useRouter();

  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [showCommentSheet, setShowCommentSheet] = useState(false);

  // Image dimensions state
  const [imageDimensions, setImageDimensions] = useState<{
    width: number;
    height: number;
  } | null>(null);

  // Linked products state
  const [linkedProducts, setLinkedProducts] = useState<ProductData[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);

  // Edit/Delete state
  const [showEditModal, setShowEditModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Full-screen image state
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Alert dialog state
  const { alertState, showAlert, hideAlert, showConfirm } = useAlert();
  const toast = useToast();

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(20)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;

  // Refs - removed postOptionsRef as it's now handled at parent level

  // Check if current user owns this post
  const { isOwner, isLoading: ownershipLoading } = usePostOwnership({
    postBusinessId:
      post.post_source === "business" ? post.author_id : undefined,
    postCustomerId:
      post.post_source === "customer" ? post.author_id : undefined,
    postSource: post.post_source,
  });

  useEffect(() => {
    // Animate card entrance
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 400,
        delay: index * 100,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim, scaleAnim, index]);

  // No longer needed - LikeButton handles its own state

  // Load linked products
  useEffect(() => {
    if (!post.product_ids || post.product_ids.length === 0) {
      setLinkedProducts([]);
      return;
    }

    const fetchProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const { data, error } = await fetchProductsByIds(post.product_ids);

        if (error) {
          console.error("Error fetching products:", error);
          setLinkedProducts([]);
          return;
        }

        // Maintain the order of products as specified in product_ids array
        const orderedProducts = post.product_ids
          .map((id) => data?.find((product: any) => product.id === id))
          .filter(Boolean) as any[];

        setLinkedProducts(orderedProducts);
      } catch (err) {
        console.error("Error fetching products:", err);
        setLinkedProducts([]);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, [post.product_ids]);

  // Get image dimensions when showActualAspectRatio is true
  useEffect(() => {
    if (showActualAspectRatio && post.image_url && !imageDimensions) {
      Image.getSize(
        post.image_url,
        (width, height) => {
          setImageDimensions({ width, height });
        },
        (error) => {
          console.error("Error getting image size:", error);
          // Fallback to default dimensions
          setImageDimensions({ width: 400, height: 300 });
        }
      );
    }
  }, [showActualAspectRatio, post.image_url, imageDimensions]);

  // Real-time subscription logic removed as per user request
  // Posts will now rely on optimistic updates and manual refresh

  // Format the date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return "Just now";
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  // Get author info based on post source
  const getAuthorInfo = () => {
    if (post.post_source === "business") {
      return {
        name: post.author_name || "Business",
        avatar: post.author_avatar,
        subtitle: post.business_slug ? `@${post.business_slug}` : "Business",
      };
    } else {
      return {
        name: post.author_name || "Customer",
        avatar: post.author_avatar,
        subtitle: "", // Don't show "Customer" text for customer posts
      };
    }
  };

  // Get complete address info like Next.js version
  const getCompleteAddress = () => {
    const addressParts = [];

    // Format each part by replacing hyphens with spaces and capitalizing
    if (post.locality_slug) {
      addressParts.push(
        post.locality_slug
          .replace(/-/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase())
      );
    }
    if (post.city_slug) {
      addressParts.push(
        post.city_slug
          .replace(/-/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase())
      );
    }
    if (post.state_slug) {
      addressParts.push(
        post.state_slug
          .replace(/-/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase())
      );
    }
    if (post.pincode) {
      addressParts.push(post.pincode);
    }

    return addressParts.length > 0 ? addressParts.join(", ") : null;
  };

  // Handle contact actions
  const handleWhatsAppPress = () => {
    if (post.whatsapp_number) {
      const whatsappNumber = post.whatsapp_number.replace(/\D/g, ""); // Remove non-digits
      const message = `Hi ${post.author_name}, I saw your post and would like to know more.`;
      const url = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(
        message
      )}`;
      Linking.openURL(url).catch(() => {
        toast.error("Could not open WhatsApp");
      });
    }
  };

  const handlePhonePress = () => {
    if (post.phone) {
      Linking.openURL(`tel:${post.phone}`).catch(() => {
        toast.error("Could not make phone call");
      });
    }
  };

  const handleCommentPress = () => {
    setShowCommentSheet(true);
  };

  const handleSharePress = async () => {
    const result = await sharePost(
      post.id,
      post.content,
      post.author_name || "Unknown",
      post.business_slug || undefined
    );

    if (!result.success && result.message !== "Share cancelled") {
      toast.error(result.message);
    }
  };

  const handleMorePress = () => {
    if (onMorePress) {
      onMorePress(post, isOwner);
    } else {
      // If no onMorePress callback is provided, show a simple share action
      handleSharePress();
    }
  };

  const handlePostPress = () => {
    router.push(generatePostPath(post.id));
  };

  const handleBusinessProfilePress = () => {
    if (post.post_source === "business" && post.business_slug) {
      router.push(`/business/${post.business_slug}`);
    }
  };

  const handleEditPost = () => {
    setShowEditModal(true);
  };

  const handleEditProducts = () => {
    // For business posts, this could open a separate product editor
    // For now, we'll use the same edit modal which includes product selector
    setShowEditModal(true);
  };

  const handleDeletePost = () => {
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);

    try {
      let result;
      if (post.post_source === "business") {
        result = await deleteBusinessPost(post.id);
      } else {
        result = await deleteCustomerPost(post.id);
      }

      if (result.success) {
        toast.success("Post deleted successfully");
        if (onPostDelete) {
          onPostDelete(post.id);
        }
        setShowDeleteDialog(false);
      } else {
        toast.error(result.message || "Failed to delete post");
      }
    } catch (error) {
      console.error("Error deleting post:", error);
      toast.error("Failed to delete post. Please try again.");
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePostUpdated = (
    postId: string,
    newContent: string,
    newImageUrl?: string | null,
    newProductIds?: string[]
  ) => {
    setShowEditModal(false);
    if (onPostUpdate) {
      onPostUpdate(postId, newContent, newImageUrl);
    }
    if (onProductsUpdate && newProductIds) {
      onProductsUpdate(postId, newProductIds);
    }
  };

  const authorInfo = getAuthorInfo();
  const completeAddress = getCompleteAddress();

  // Theme colors
  const backgroundColor = isDark ? "#000000" : "#FFFFFF";
  const borderColor = isDark ? "#000000" : "#FFFFFF"; // Seamless separators
  const textColor = isDark ? "#FFFFFF" : "#000000";
  const subtitleColor = isDark ? "#9CA3AF" : "#6B7280";
  const goldColor = "#D4AF37";

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor,
          borderColor,
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }, { scale: scaleAnim }],
        },
      ]}
    >
      {/* Header */}
      <View style={styles.header}>
        {/* Author Info - Clickable for business posts with business_slug */}
        {post.post_source === "business" && post.business_slug ? (
          <TouchableOpacity
            style={styles.authorInfo}
            onPress={handleBusinessProfilePress}
            activeOpacity={0.7}
          >
            {/* Avatar */}
            <View style={styles.avatarContainer}>
              {authorInfo.avatar ? (
                <Image
                  source={{ uri: authorInfo.avatar }}
                  style={styles.avatar}
                />
              ) : (
                <View
                  style={[
                    styles.defaultAvatar,
                    { backgroundColor: isDark ? "#374151" : "#F3F4F6" },
                  ]}
                >
                  <User size={24} color={isDark ? "#9CA3AF" : "#6B7280"} />
                </View>
              )}
            </View>

            {/* Author details */}
            <View style={styles.authorDetails}>
              {/* Business name */}
              <Text style={[styles.authorName, { color: textColor }]}>
                {authorInfo.name}
              </Text>

              {/* Business slug */}
              <Text style={[styles.businessSlug, { color: subtitleColor }]}>
                @{post.business_slug}
              </Text>

              {/* Timestamp - below business slug */}
              <Text style={[styles.timestampText, { color: subtitleColor }]}>
                {formatDate(post.created_at)}
              </Text>
            </View>
          </TouchableOpacity>
        ) : (
          <View style={styles.authorInfo}>
            {/* Avatar */}
            <View style={styles.avatarContainer}>
              {authorInfo.avatar ? (
                <Image
                  source={{ uri: authorInfo.avatar }}
                  style={styles.avatar}
                />
              ) : (
                <View
                  style={[
                    styles.defaultAvatar,
                    { backgroundColor: isDark ? "#374151" : "#F3F4F6" },
                  ]}
                >
                  <User size={24} color={isDark ? "#9CA3AF" : "#6B7280"} />
                </View>
              )}
            </View>

            {/* Author details */}
            <View style={styles.authorDetails}>
              {/* Business/Customer name */}
              <Text style={[styles.authorName, { color: textColor }]}>
                {authorInfo.name}
              </Text>

              {/* Business slug - only for business posts */}
              {post.post_source === "business" && post.business_slug && (
                <Text style={[styles.businessSlug, { color: subtitleColor }]}>
                  @{post.business_slug}
                </Text>
              )}

              {/* Timestamp - moved from bottom */}
              <Text style={[styles.timestampText, { color: subtitleColor }]}>
                {formatDate(post.created_at)}
              </Text>
            </View>
          </View>
        )}

        {/* More button - show for all users (share is available to everyone) */}
        {!ownershipLoading && (
          <TouchableOpacity
            style={[
              styles.moreButton,
              {
                backgroundColor: "transparent",
              },
            ]}
            onPress={handleMorePress}
            disabled={isDeleting}
            activeOpacity={0.6}
          >
            <MoreVertical size={20} color={subtitleColor} />
          </TouchableOpacity>
        )}
      </View>

      {/* Address section below header - like Next.js */}
      {completeAddress && (
        <View style={styles.addressSectionBelowHeader}>
          <MapPin size={14} color={subtitleColor} />
          <Text style={[styles.addressBelowHeader, { color: subtitleColor }]}>
            {completeAddress}
          </Text>
        </View>
      )}

      {/* Content */}
      {disablePostClick ? (
        <View style={styles.content}>
          <Text style={[styles.postText, { color: textColor }]}>
            {post.content}
          </Text>
        </View>
      ) : (
        <TouchableOpacity
          style={styles.content}
          onPress={handlePostPress}
          activeOpacity={0.8}
        >
          <Text style={[styles.postText, { color: textColor }]}>
            {post.content}
          </Text>
        </TouchableOpacity>
      )}

      {/* Image */}
      {post.image_url && !imageError && (
        <TouchableOpacity
          style={[
            styles.imageContainer,
            showActualAspectRatio && {
              aspectRatio: undefined, // Remove fixed aspect ratio for actual ratio display
            },
          ]}
          onPress={
            disablePostClick
              ? enableImageFullscreen
                ? () => setShowFullscreenImage(true)
                : undefined
              : handlePostPress
          }
          activeOpacity={0.9}
          disabled={disablePostClick && !enableImageFullscreen}
        >
          <Image
            source={{ uri: post.image_url }}
            style={[
              showActualAspectRatio
                ? styles.postImageActualRatio
                : styles.postImage,
              showActualAspectRatio &&
                imageDimensions && {
                  aspectRatio: imageDimensions.width / imageDimensions.height,
                },
            ]}
            onLoad={() => setImageLoading(false)}
            onError={() => {
              setImageError(true);
              setImageLoading(false);
            }}
            resizeMode={showActualAspectRatio ? "contain" : "cover"}
          />
          {imageLoading && (
            <View style={styles.imageLoader}>
              <View style={styles.imagePlaceholder} />
            </View>
          )}
        </TouchableOpacity>
      )}

      {/* Linked Products - Only for business posts with products */}
      {post.post_source === "business" &&
        post.product_ids &&
        post.product_ids.length > 0 && (
          <View style={styles.productsSection}>
            <View style={styles.productsSectionHeader}>
              <Package size={16} color={goldColor} />
              <Text style={[styles.productsSectionTitle, { color: textColor }]}>
                Linked Products ({linkedProducts.length})
              </Text>
            </View>

            {isLoadingProducts ? (
              <View style={styles.productsLoader}>
                <ActivityIndicator size="small" color={goldColor} />
                <Text style={[styles.loadingText, { color: subtitleColor }]}>
                  Loading products...
                </Text>
              </View>
            ) : linkedProducts.length > 0 ? (
              <FlatList
                data={linkedProducts}
                renderItem={({ item }) => (
                  <ProductCard
                    product={item}
                    isClickable={true}
                    variant="compact"
                    width={160} // Fixed width for horizontal scrolling
                  />
                )}
                keyExtractor={(item) => item.id}
                horizontal={true}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.horizontalProductsContainer}
                ItemSeparatorComponent={() => (
                  <View style={styles.productSeparator} />
                )}
              />
            ) : (
              <View style={styles.productsLoader}>
                <Text style={[styles.loadingText, { color: subtitleColor }]}>
                  No products found
                </Text>
              </View>
            )}
          </View>
        )}

      {/* Post Actions - Social interactions and Contact actions */}
      <View style={styles.postActionsContainer}>
        {/* Left side - Social interaction icons */}
        <View style={styles.socialActions}>
          <LikeButton
            postId={post.id}
            postSource={post.post_source}
            size="sm"
            variant="minimal"
            showCount={true}
          />
          <TouchableOpacity
            style={styles.socialButton}
            onPress={handleCommentPress}
          >
            <MessageCircle size={20} color={isDark ? "#FFFFFF" : "#000000"} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.socialButton}
            onPress={handleSharePress}
          >
            <Share2 size={20} color={isDark ? "#FFFFFF" : "#000000"} />
          </TouchableOpacity>
        </View>

        {/* Right side - Contact actions (only for business posts) */}
        {post.post_source === "business" &&
          (post.whatsapp_number || post.phone) && (
            <View style={styles.contactActionsRight}>
              {post.whatsapp_number && (
                <TouchableOpacity
                  style={styles.socialButton}
                  onPress={handleWhatsAppPress}
                >
                  <WhatsAppIcon
                    size={20}
                    color={isDark ? "#FFFFFF" : "#000000"}
                  />
                </TouchableOpacity>
              )}

              {post.phone && (
                <TouchableOpacity
                  style={styles.socialButton}
                  onPress={handlePhonePress}
                >
                  <Phone size={20} color={isDark ? "#FFFFFF" : "#000000"} />
                </TouchableOpacity>
              )}
            </View>
          )}
      </View>

      {/* Edit Modals */}
      {post.post_source === "customer" ? (
        <CustomerPostEditModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          postId={post.id}
          initialContent={post.content}
          initialImageUrl={post.image_url}
          customerName={post.author_name || undefined}
          onPostUpdated={handlePostUpdated}
        />
      ) : (
        <BusinessPostEditModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          postId={post.id}
          initialContent={post.content}
          initialImageUrl={post.image_url}
          initialProductIds={post.product_ids || []}
          businessName={post.author_name || undefined}
          onPostUpdated={handlePostUpdated}
        />
      )}

      {/* Full-screen Image Modal */}
      {enableImageFullscreen && showFullscreenImage && post.image_url && (
        <Modal
          visible={showFullscreenImage}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowFullscreenImage(false)}
        >
          <View style={styles.fullscreenContainer}>
            <TouchableOpacity
              style={styles.fullscreenCloseButton}
              onPress={() => setShowFullscreenImage(false)}
            >
              <Text style={styles.fullscreenCloseText}>✕</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.fullscreenImageContainer}
              activeOpacity={1}
              onPress={() => setShowFullscreenImage(false)}
            >
              <Image
                source={{ uri: post.image_url }}
                style={styles.fullscreenImage}
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
        </Modal>
      )}

      {/* Alert Dialog */}
      <AlertDialog
        visible={alertState.visible}
        type={alertState.type}
        title={alertState.title}
        message={alertState.message}
        buttons={alertState.buttons || []}
        onClose={hideAlert}
        showCloseButton={alertState.showCloseButton}
        customIcon={alertState.customIcon}
      />

      {/* Delete Dialog */}
      <PostDeleteDialog
        visible={showDeleteDialog}
        onClose={() => setShowDeleteDialog(false)}
        onConfirm={handleDeleteConfirm}
        isDeleting={isDeleting}
      />

      {/* Comment Bottom Sheet */}
      <CommentBottomSheet
        isVisible={showCommentSheet}
        onClose={() => setShowCommentSheet(false)}
        postId={post.id}
        postSource={post.post_source}
      >
        <CommentSection
          postId={post.id}
          postSource={post.post_source}
          showInput={true}
        />
      </CommentBottomSheet>
    </Animated.View>
  );
}
