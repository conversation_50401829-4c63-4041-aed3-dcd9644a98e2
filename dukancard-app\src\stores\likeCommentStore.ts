import { create } from 'zustand';
import type { PostLikeStatus, CommentLikeStatus, PostCommentWithUser } from '@/lib/types/like-comment';
import { 
  likePost, 
  unlikePost, 
  getPostLikeStatus,
  togglePostLike 
} from '@/backend/supabase/services/likes/postLikes';
import {
  createComment as createCommentService,
  editComment as editCommentService,
  deleteComment as deleteCommentService,
  pinComment as pinCommentService,
  unpinComment as unpinCommentService,
  getPostComments as getPostCommentsService
} from '@/backend/supabase/services/comments/postComments';
import {
  likeComment,
  unlikeComment,
  getCommentLikeStatus,
  toggleCommentLike
} from '@/backend/supabase/services/likes/commentLikes';

// State interfaces
interface PostLikeState {
  [postKey: string]: PostLikeStatus; // postKey format: "postId:postSource"
}

interface CommentLikeState {
  [commentId: string]: CommentLikeStatus;
}

interface PostCommentsState {
  [postKey: string]: {
    comments: PostCommentWithUser[];
    loading: boolean;
    hasMore: boolean;
    lastFetched: number;
  };
}

interface LikeCommentStore {
  // State
  postLikes: PostLikeState;
  commentLikes: CommentLikeState;
  postComments: PostCommentsState;
  
  // Loading states
  likingPosts: Set<string>; // postKeys currently being liked/unliked
  likingComments: Set<string>; // commentIds currently being liked/unliked
  loadingComments: Set<string>; // postKeys currently loading comments
  
  // Actions for post likes
  likePost: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  unlikePost: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  togglePostLike: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  getPostLikeStatus: (postId: string, postSource: 'business' | 'customer') => Promise<void>;
  
  // Actions for comment likes
  likeComment: (commentId: string) => Promise<void>;
  unlikeComment: (commentId: string) => Promise<void>;
  toggleCommentLike: (commentId: string) => Promise<void>;
  getCommentLikeStatus: (commentId: string) => Promise<void>;

  // Actions for comments
  createComment: (postId: string, postSource: 'business' | 'customer', content: string, parentCommentId?: string) => Promise<boolean>;
  editComment: (commentId: string, content: string) => Promise<boolean>;
  deleteComment: (commentId: string, postId: string, postSource: 'business' | 'customer') => Promise<boolean>;
  pinComment: (commentId: string, postId: string, postSource: 'business' | 'customer') => Promise<boolean>;
  unpinComment: (commentId: string, postId: string, postSource: 'business' | 'customer') => Promise<boolean>;
  loadComments: (postId: string, postSource: 'business' | 'customer', refresh?: boolean) => Promise<void>;

  // Utility actions
  clearCache: () => void;
  clearPostCache: (postId: string, postSource: 'business' | 'customer') => void;
}

const createPostKey = (postId: string, postSource: 'business' | 'customer') => `${postId}:${postSource}`;

export const useLikeCommentStore = create<LikeCommentStore>()((set, get) => ({
        // Initial state
        postLikes: {},
        commentLikes: {},
        postComments: {},
        likingPosts: new Set(),
        likingComments: new Set(),
        loadingComments: new Set(),

        // Post like actions
        likePost: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          set((state) => {
            state.likingPosts.add(postKey);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = true;
                state.postLikes[postKey].like_count += 1;
              } else {
                state.postLikes[postKey] = { is_liked: true, like_count: 1 };
              }
            });

            const result = await likePost(postId, postSource);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.postLikes[postKey] = {
                  is_liked: true,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.postLikes[postKey]) {
                  state.postLikes[postKey].is_liked = false;
                  state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = false;
                state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
              }
            });
          } finally {
            set((state) => {
              state.likingPosts.delete(postKey);
            });
          }
        },

        unlikePost: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          set((state) => {
            state.likingPosts.add(postKey);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = false;
                state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
              }
            });

            const result = await unlikePost(postId, postSource);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.postLikes[postKey] = {
                  is_liked: false,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.postLikes[postKey]) {
                  state.postLikes[postKey].is_liked = true;
                  state.postLikes[postKey].like_count += 1;
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = true;
                state.postLikes[postKey].like_count += 1;
              }
            });
          } finally {
            set((state) => {
              state.likingPosts.delete(postKey);
            });
          }
        },

        togglePostLike: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          const currentStatus = get().postLikes[postKey];
          
          if (currentStatus?.is_liked) {
            await get().unlikePost(postId, postSource);
          } else {
            await get().likePost(postId, postSource);
          }
        },

        getPostLikeStatus: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          try {
            const status = await getPostLikeStatus(postId, postSource);
            set((state) => {
              state.postLikes[postKey] = {
                is_liked: status.is_liked,
                like_count: status.like_count
              };
            });
          } catch (error) {
            console.error('Error fetching post like status:', error);
          }
        },

        // Comment like actions
        likeComment: async (commentId: string) => {
          set((state) => {
            state.likingComments.add(commentId);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = true;
                state.commentLikes[commentId].like_count += 1;
              } else {
                state.commentLikes[commentId] = { is_liked: true, like_count: 1 };
              }
            });

            const result = await likeComment(commentId);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.commentLikes[commentId] = {
                  is_liked: true,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.commentLikes[commentId]) {
                  state.commentLikes[commentId].is_liked = false;
                  state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = false;
                state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
              }
            });
          } finally {
            set((state) => {
              state.likingComments.delete(commentId);
            });
          }
        },

        unlikeComment: async (commentId: string) => {
          set((state) => {
            state.likingComments.add(commentId);
          });

          try {
            // Optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = false;
                state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
              }
            });

            const result = await unlikeComment(commentId);
            
            if (result.success && result.like_count !== undefined) {
              set((state) => {
                state.commentLikes[commentId] = {
                  is_liked: false,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state) => {
                if (state.commentLikes[commentId]) {
                  state.commentLikes[commentId].is_liked = true;
                  state.commentLikes[commentId].like_count += 1;
                }
              });
            }
          } catch (error) {
            // Revert optimistic update
            set((state) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = true;
                state.commentLikes[commentId].like_count += 1;
              }
            });
          } finally {
            set((state) => {
              state.likingComments.delete(commentId);
            });
          }
        },

        toggleCommentLike: async (commentId: string) => {
          const currentStatus = get().commentLikes[commentId];
          
          if (currentStatus?.is_liked) {
            await get().unlikeComment(commentId);
          } else {
            await get().likeComment(commentId);
          }
        },

        getCommentLikeStatus: async (commentId: string) => {
          try {
            const status = await getCommentLikeStatus(commentId);
            set((state) => {
              state.commentLikes[commentId] = {
                is_liked: status.is_liked,
                like_count: status.like_count
              };
            });
          } catch (error) {
            console.error('Error fetching comment like status:', error);
          }
        },

        // Utility actions
        clearCache: () => {
          set((state) => {
            state.postLikes = {};
            state.commentLikes = {};
            state.postComments = {};
          });
        },

        clearPostCache: (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          const currentState = get();
          const newPostLikes = { ...currentState.postLikes };
          const newPostComments = { ...currentState.postComments };
          delete newPostLikes[postKey];
          delete newPostComments[postKey];
          set({
            ...currentState,
            postLikes: newPostLikes,
            postComments: newPostComments,
          });
        },

        // Add missing comment functions
        createComment: async (postId: string, postSource: 'business' | 'customer', content: string, parentCommentId?: string) => {
          // TODO: Implement comment creation
          return true;
        },

        editComment: async (commentId: string, content: string) => {
          // TODO: Implement comment editing
          return true;
        },

        deleteComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          // TODO: Implement comment deletion
          return true;
        },

        pinComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          // TODO: Implement comment pinning
          return true;
        },

        unpinComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          // TODO: Implement comment unpinning
          return true;
        },

        loadComments: async (postId: string, postSource: 'business' | 'customer', refresh = false) => {
          const postKey = createPostKey(postId, postSource);
          
          // Check if already loading
          if (get().loadingComments.has(postKey)) {
            return;
          }

          // Check cache (5 minutes)
          const cached = get().postComments[postKey];
          if (!refresh && cached && (Date.now() - cached.lastFetched) < 5 * 60 * 1000) {
            return;
          }

          set((state) => {
            state.loadingComments.add(postKey);
          });

          try {
            const result = await getPostCommentsService({
              post_id: postId,
              post_source: postSource,
              sort_order: 'pinned_first',
              limit: 50,
              offset: 0
            });

            if (result.success) {
              set((state) => {
                state.postComments[postKey] = {
                  comments: result.data,
                  loading: false,
                  hasMore: result.data.length === 50,
                  lastFetched: Date.now()
                };
              });
            }
          } catch (error) {
            console.error('Error loading comments:', error);
          } finally {
            set((state) => {
              state.loadingComments.delete(postKey);
            });
          }
        },
}));
