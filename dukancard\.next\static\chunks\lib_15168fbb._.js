(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/actions/shared/delete-customer-post-media.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_next_headers_c234521c.js",
  "static/chunks/_28823ebb._.js",
  "static/chunks/lib_actions_shared_delete-customer-post-media_ts_7ba4e0fd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/shared/delete-customer-post-media.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/utils/client-image-compression.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_utils_client-image-compression_ts_45be4be3._.js",
  "static/chunks/lib_utils_client-image-compression_ts_7ba4e0fd._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/utils/client-image-compression.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/actions/shared/upload-customer-post-media.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_actions_shared_95d73c2f._.js",
  "static/chunks/lib_actions_shared_upload-customer-post-media_ts_eb30c2d5._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/shared/upload-customer-post-media.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/actions/customerPosts/index.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_actions_customerPosts_index_ts_5470b022._.js",
  "static/chunks/lib_actions_customerPosts_index_ts_eb30c2d5._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/actions/customerPosts/index.ts [app-client] (ecmascript)");
    });
});
}}),
}]);