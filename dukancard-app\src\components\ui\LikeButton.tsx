import React, { useEffect, useState } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  Animated,
  Pressable,
  Alert,
} from 'react-native';
import { Heart } from 'lucide-react-native';
import { useLikeCommentStore } from '@/src/stores/likeCommentStore';
import { formatIndianNumberShort } from '@/src/utils/formatNumber';

interface LikeButtonProps {
  postId: string;
  postSource: 'business' | 'customer';
  initialLikeCount?: number;
  initialIsLiked?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal';
  showCount?: boolean;
  disabled?: boolean;
  onLikeChange?: (isLiked: boolean, likeCount: number) => void;
  onLongPress?: () => void;
}

export function LikeButton({
  postId,
  postSource,
  initialLikeCount = 0,
  initialIsLiked = false,
  size = 'md',
  variant = 'default',
  showCount = true,
  disabled = false,
  onLikeChange,
  onLongPress,
}: LikeButtonProps) {
  const [scaleAnim] = useState(new Animated.Value(1));
  const [heartAnim] = useState(new Animated.Value(1));

  const {
    postLikes,
    likingPosts,
    togglePostLike,
    getPostLikeStatus,
  } = useLikeCommentStore();

  const postKey = `${postId}:${postSource}`;
  const postLikeStatus = postLikes[postKey];
  const isLoading = likingPosts.has(postKey);

  // Use store state if available, otherwise use initial values
  const isLiked = postLikeStatus?.is_liked ?? initialIsLiked;
  const likeCount = postLikeStatus?.like_count ?? initialLikeCount;

  // Load initial like status if not in store
  useEffect(() => {
    if (!postLikeStatus) {
      getPostLikeStatus(postId, postSource);
    }
  }, [postId, postSource, postLikeStatus, getPostLikeStatus]);

  // Notify parent of changes
  useEffect(() => {
    if (onLikeChange && postLikeStatus) {
      onLikeChange(postLikeStatus.is_liked, postLikeStatus.like_count);
    }
  }, [postLikeStatus, onLikeChange]);

  const handlePress = async () => {
    if (disabled || isLoading) return;

    // Animate button press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Animate heart if liking
    if (!isLiked) {
      Animated.sequence([
        Animated.timing(heartAnim, {
          toValue: 1.3,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(heartAnim, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }

    try {
      await togglePostLike(postId, postSource);
    } catch (error) {
      console.error('Error toggling like:', error);
      Alert.alert('Error', 'Failed to update like. Please try again.');
    }
  };

  const handleLongPress = () => {
    if (onLongPress) {
      onLongPress();
    } else {
      // Default behavior: show who liked
      Alert.alert(
        'Likes',
        `${likeCount} ${likeCount === 1 ? 'person likes' : 'people like'} this post`,
        [{ text: 'OK' }]
      );
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      iconSize: 16,
      fontSize: 12,
      padding: 8,
      gap: 4,
    },
    md: {
      iconSize: 20,
      fontSize: 14,
      padding: 12,
      gap: 6,
    },
    lg: {
      iconSize: 24,
      fontSize: 16,
      padding: 16,
      gap: 8,
    },
  };

  const config = sizeConfig[size];

  const buttonStyle = [
    styles.button,
    variant === 'default' && [
      styles.defaultButton,
      isLiked ? styles.likedButton : styles.unlikedButton,
    ],
    variant === 'minimal' && styles.minimalButton,
    { padding: config.padding },
    disabled && styles.disabled,
  ];

  const textStyle = [
    styles.text,
    { fontSize: config.fontSize },
    variant === 'default' && (isLiked ? styles.likedText : styles.unlikedText),
    variant === 'minimal' && (isLiked ? styles.minimalLikedText : styles.minimalUnlikedText),
    disabled && styles.disabledText,
  ];

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <Pressable
        style={buttonStyle}
        onPress={handlePress}
        onLongPress={handleLongPress}
        disabled={disabled || isLoading}
        android_ripple={{ color: isLiked ? '#fecaca' : '#f3f4f6', borderless: true }}
      >
        <Animated.View style={{ transform: [{ scale: heartAnim }] }}>
          <Heart
            size={config.iconSize}
            color={
              variant === 'default'
                ? isLiked
                  ? '#dc2626'
                  : '#6b7280'
                : isLiked
                ? '#dc2626'
                : '#6b7280'
            }
            fill={isLiked ? '#dc2626' : 'none'}
          />
        </Animated.View>

        {showCount && (
          <Text style={textStyle}>
            {formatIndianNumberShort(likeCount)}
          </Text>
        )}

        {isLoading && (
          <View style={styles.loadingIndicator}>
            <Text style={[textStyle, { fontSize: config.fontSize - 2 }]}>...</Text>
          </View>
        )}
      </Pressable>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  defaultButton: {
    borderWidth: 1,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  likedButton: {
    backgroundColor: '#fef2f2',
    borderColor: '#fecaca',
  },
  unlikedButton: {
    backgroundColor: '#ffffff',
    borderColor: '#e5e7eb',
  },
  minimalButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: 4,
    paddingVertical: 4,
  },
  text: {
    fontWeight: '600',
    marginLeft: 6,
  },
  likedText: {
    color: '#dc2626',
  },
  unlikedText: {
    color: '#6b7280',
  },
  minimalLikedText: {
    color: '#dc2626',
  },
  minimalUnlikedText: {
    color: '#6b7280',
  },
  disabled: {
    opacity: 0.5,
  },
  disabledText: {
    opacity: 0.5,
  },
  loadingIndicator: {
    marginLeft: 4,
  },
});

// Hook for using like button functionality without UI
export function useLikeButton(postId: string, postSource: 'business' | 'customer') {
  const {
    postLikes,
    likingPosts,
    togglePostLike,
    getPostLikeStatus,
  } = useLikeCommentStore();

  const postKey = `${postId}:${postSource}`;
  const postLikeStatus = postLikes[postKey];
  const isLoading = likingPosts.has(postKey);

  const isLiked = postLikeStatus?.is_liked ?? false;
  const likeCount = postLikeStatus?.like_count ?? 0;

  // Load initial like status if not in store
  useEffect(() => {
    if (!postLikeStatus) {
      getPostLikeStatus(postId, postSource);
    }
  }, [postId, postSource, postLikeStatus, getPostLikeStatus]);

  const handleToggle = async () => {
    if (isLoading) return;
    
    try {
      await togglePostLike(postId, postSource);
    } catch (error) {
      console.error('Error toggling like:', error);
      throw error;
    }
  };

  return {
    isLiked,
    likeCount,
    isLoading,
    toggleLike: handleToggle,
  };
}

export default LikeButton;
