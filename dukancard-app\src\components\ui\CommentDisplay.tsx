import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
} from 'react-native';
import { Heart, MessageCircle, Pin, MoreHorizontal, Edit, Trash2, Flag } from 'lucide-react-native';
import { useLikeCommentStore } from '@/src/stores/simpleLikeCommentStore';
import { formatIndianNumberShort } from '@/src/utils/formatNumber';
import type { PostCommentWithUser } from '@/lib/types/like-comment';
import CommentInput from './CommentInput';

interface CommentDisplayProps {
  comment: PostCommentWithUser;
  postId: string;
  postSource: 'business' | 'customer';
  currentUserId?: string;
  isPostOwner?: boolean;
  level?: number;
  onReply?: (commentId: string) => void;
  onEdit?: (commentId: string, content: string) => void;
  onDelete?: (commentId: string) => void;
  onPin?: (commentId: string) => void;
  onUnpin?: (commentId: string) => void;
}

export function CommentDisplay({
  comment,
  postId,
  postSource,
  currentUserId,
  isPostOwner = false,
  level = 0,
  onReply,
  onEdit,
  onDelete,
  onPin,
  onUnpin,
}: CommentDisplayProps) {
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [showEditInput, setShowEditInput] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [scaleAnim] = useState(new Animated.Value(1));
  
  const {
    commentLikes,
    likingComments,
    toggleCommentLike,
    getCommentLikeStatus,
    editComment,
    deleteComment,
    pinComment,
    unpinComment,
  } = useLikeCommentStore();

  const commentLikeStatus = commentLikes[comment.id];
  const isLikingComment = likingComments.has(comment.id);
  
  // Use store state if available, otherwise use comment data
  const isLiked = commentLikeStatus?.is_liked ?? comment.is_liked_by_current_user;
  const likeCount = commentLikeStatus?.like_count ?? comment.like_count;

  const isOwner = currentUserId === comment.user_id;
  const canEdit = isOwner;
  const canDelete = isOwner || isPostOwner;
  const canPin = isPostOwner && !comment.parent_comment_id; // Only pin top-level comments
  const canReply = level === 0; // Only allow replies to top-level comments

  // Load comment like status if not in store
  useEffect(() => {
    if (!commentLikeStatus) {
      getCommentLikeStatus(comment.id);
    }
  }, [comment.id, commentLikeStatus, getCommentLikeStatus]);

  const handleLike = async () => {
    if (isLikingComment) return;
    
    // Animate like button
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.2,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();
    
    try {
      await toggleCommentLike(comment.id);
    } catch (error) {
      console.error('Error toggling comment like:', error);
      Alert.alert('Error', 'Failed to update like. Please try again.');
    }
  };

  const handleReply = () => {
    setShowReplyInput(true);
    onReply?.(comment.id);
  };

  const handleEdit = () => {
    setShowEditInput(true);
    setShowActions(false);
  };

  const handleEditSubmit = async (content: string) => {
    try {
      const success = await editComment(comment.id, content);
      if (success) {
        setShowEditInput(false);
        onEdit?.(comment.id, content);
        Alert.alert('Success', 'Comment updated successfully!');
      } else {
        Alert.alert('Error', 'Failed to update comment. Please try again.');
      }
    } catch (error) {
      console.error('Error editing comment:', error);
      Alert.alert('Error', 'Failed to update comment. Please try again.');
    }
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Comment',
      'Are you sure you want to delete this comment?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              const success = await deleteComment(comment.id, postId, postSource);
              if (success) {
                onDelete?.(comment.id);
                Alert.alert('Success', 'Comment deleted successfully!');
              } else {
                Alert.alert('Error', 'Failed to delete comment. Please try again.');
              }
            } catch (error) {
              console.error('Error deleting comment:', error);
              Alert.alert('Error', 'Failed to delete comment. Please try again.');
            }
          },
        },
      ]
    );
    setShowActions(false);
  };

  const handlePin = async () => {
    try {
      if (comment.is_pinned) {
        const success = await unpinComment(comment.id, postId, postSource);
        if (success) {
          onUnpin?.(comment.id);
          Alert.alert('Success', 'Comment unpinned successfully!');
        }
      } else {
        const success = await pinComment(comment.id, postId, postSource);
        if (success) {
          onPin?.(comment.id);
          Alert.alert('Success', 'Comment pinned successfully!');
        }
      }
    } catch (error) {
      console.error('Error toggling pin status:', error);
      Alert.alert('Error', 'Failed to update pin status. Please try again.');
    }
    setShowActions(false);
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d`;
    
    return date.toLocaleDateString();
  };

  const getUserInitial = (name: string) => {
    return name.charAt(0).toUpperCase();
  };

  return (
    <View style={[styles.container, level > 0 && styles.replyContainer]}>
      <View style={styles.commentContent}>
        {/* Avatar */}
        <View style={styles.avatarContainer}>
          <View style={[
            styles.avatar,
            { backgroundColor: comment.user_type === 'business' ? '#3b82f6' : '#10b981' }
          ]}>
            <Text style={styles.avatarText}>
              {getUserInitial(comment.user_name)}
            </Text>
          </View>
        </View>

        {/* Content */}
        <View style={styles.contentContainer}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={styles.userName}>{comment.user_name}</Text>
              
              <View style={[
                styles.userBadge,
                { backgroundColor: comment.user_type === 'business' ? '#dbeafe' : '#d1fae5' }
              ]}>
                <Text style={[
                  styles.userBadgeText,
                  { color: comment.user_type === 'business' ? '#1d4ed8' : '#059669' }
                ]}>
                  {comment.user_type === 'business' ? 'Business' : 'Customer'}
                </Text>
              </View>

              {comment.is_pinned && (
                <View style={styles.pinnedBadge}>
                  <Pin size={12} color="#f59e0b" />
                  <Text style={styles.pinnedText}>Pinned</Text>
                </View>
              )}
            </View>

            <View style={styles.headerRight}>
              <Text style={styles.timestamp}>
                {formatTimeAgo(comment.created_at)}
              </Text>
              
              {comment.is_edited && (
                <Text style={styles.editedText}>(edited)</Text>
              )}

              <TouchableOpacity
                style={styles.moreButton}
                onPress={() => setShowActions(!showActions)}
              >
                <MoreHorizontal size={16} color="#6b7280" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Comment text or edit input */}
          {showEditInput ? (
            <View style={styles.editContainer}>
              <CommentInput
                postId={postId}
                postSource={postSource}
                placeholder="Edit your comment..."
                autoFocus
                showCancel
                onSubmit={handleEditSubmit}
                onCancel={() => setShowEditInput(false)}
              />
            </View>
          ) : (
            <Text style={styles.commentText}>{comment.content}</Text>
          )}

          {/* Actions */}
          <View style={styles.actions}>
            <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleLike}
                disabled={isLikingComment}
              >
                <Heart
                  size={16}
                  color={isLiked ? '#ef4444' : '#6b7280'}
                  fill={isLiked ? '#ef4444' : 'none'}
                />
                {likeCount > 0 && (
                  <Text style={[
                    styles.actionText,
                    { color: isLiked ? '#ef4444' : '#6b7280' }
                  ]}>
                    {formatIndianNumberShort(likeCount)}
                  </Text>
                )}
              </TouchableOpacity>
            </Animated.View>

            {canReply && (
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleReply}
              >
                <MessageCircle size={16} color="#6b7280" />
                <Text style={styles.actionText}>Reply</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Actions menu */}
          {showActions && (
            <View style={styles.actionsMenu}>
              {canEdit && (
                <TouchableOpacity style={styles.menuItem} onPress={handleEdit}>
                  <Edit size={16} color="#6b7280" />
                  <Text style={styles.menuItemText}>Edit</Text>
                </TouchableOpacity>
              )}
              
              {canPin && (
                <TouchableOpacity style={styles.menuItem} onPress={handlePin}>
                  <Pin size={16} color="#6b7280" />
                  <Text style={styles.menuItemText}>
                    {comment.is_pinned ? 'Unpin' : 'Pin'}
                  </Text>
                </TouchableOpacity>
              )}
              
              {canDelete && (
                <TouchableOpacity style={styles.menuItem} onPress={handleDelete}>
                  <Trash2 size={16} color="#ef4444" />
                  <Text style={[styles.menuItemText, { color: '#ef4444' }]}>Delete</Text>
                </TouchableOpacity>
              )}
              
              {!isOwner && (
                <TouchableOpacity style={styles.menuItem} onPress={() => {/* TODO: Implement report */}}>
                  <Flag size={16} color="#6b7280" />
                  <Text style={styles.menuItemText}>Report</Text>
                </TouchableOpacity>
              )}
            </View>
          )}

          {/* Reply input */}
          {showReplyInput && (
            <View style={styles.replyContainer}>
              <CommentInput
                postId={postId}
                postSource={postSource}
                parentCommentId={comment.id}
                placeholder={`Reply to ${comment.user_name}...`}
                autoFocus
                showCancel
                replyToUser={comment.user_name}
                onSubmit={() => setShowReplyInput(false)}
                onCancel={() => setShowReplyInput(false)}
              />
            </View>
          )}

          {/* Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <View style={styles.repliesContainer}>
              {comment.replies.map((reply) => (
                <CommentDisplay
                  key={reply.id}
                  comment={reply}
                  postId={postId}
                  postSource={postSource}
                  currentUserId={currentUserId}
                  isPostOwner={isPostOwner}
                  level={level + 1}
                  onEdit={onEdit}
                  onDelete={onDelete}
                />
              ))}
            </View>
          )}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  replyContainer: {
    marginLeft: 32,
    borderLeftWidth: 2,
    borderLeftColor: '#f3f4f6',
    paddingLeft: 16,
  },
  commentContent: {
    flexDirection: 'row',
  },
  avatarContainer: {
    marginRight: 12,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    marginRight: 8,
  },
  userBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginRight: 8,
  },
  userBadgeText: {
    fontSize: 10,
    fontWeight: '600',
  },
  pinnedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fef3c7',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginRight: 8,
  },
  pinnedText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#f59e0b',
    marginLeft: 2,
  },
  timestamp: {
    fontSize: 12,
    color: '#6b7280',
    marginRight: 4,
  },
  editedText: {
    fontSize: 10,
    color: '#9ca3af',
    fontStyle: 'italic',
    marginRight: 8,
  },
  moreButton: {
    padding: 4,
  },
  commentText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 8,
  },
  editContainer: {
    marginBottom: 8,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    paddingVertical: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    color: '#6b7280',
  },
  actionsMenu: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e5e7eb',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 8,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  menuItemText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
  },
  repliesContainer: {
    marginTop: 8,
  },
});

export default CommentDisplay;
