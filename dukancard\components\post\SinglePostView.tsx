'use client';

import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import UnifiedPostCard from '@/components/feed/shared/UnifiedPostCard';
import CommentSection from '@/components/ui/CommentSection';
import { useEffect, useState } from 'react';

interface SinglePostViewProps {
  post: UnifiedPost;
}

/**
 * Client component for displaying a single post
 * Uses the same UnifiedPostCard component as the feed for exact consistency
 */
export default function SinglePostView({ post }: SinglePostViewProps) {
  const [currentUserId, setCurrentUserId] = useState<string | undefined>();

  // TODO: Get current user ID from auth context
  useEffect(() => {
    // This would typically come from your auth context
    // setCurrentUserId(user?.id);
  }, []);

  // Determine if current user is the post owner
  const isPostOwner = currentUserId === post.author_id;

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Post Card */}
      <UnifiedPostCard
        post={post}
        index={0}
        showActualAspectRatio={true}
        disablePostClick={true}
        enableImageFullscreen={true}
      />

      {/* Comments Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <CommentSection
          postId={post.id}
          postSource={post.post_source}
          currentUserId={currentUserId}
          isPostOwner={isPostOwner}
          showInput={true}
          maxHeight="600px"
        />
      </div>
    </div>
  );
}




